# -*- coding: utf-8 -*-
"""
主窗口模块

URDF整合查看器的主窗口实现
"""

import sys
import os
import numpy as np
import time
import xml.etree.ElementTree as ET
from typing import Dict, List, Tuple, Optional

import pyqtgraph.opengl as gl
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QFileDialog, QScrollArea, QSplitter)
from PySide6.QtCore import Qt, QTimer, Slot
from PySide6.QtGui import QColor

from ..core.urdf_parser import URDFJointAnalyzer, FingerGroupAnalyzer
from ..core.kinematics import SimpleTransformCalculator
from ..core.loader import URDFLoadWorker
from ..rendering.mesh_renderer import MeshRenderer
from ..rendering.camera_controller import CameraController
from ..utils.constants import DEFAULT_COLORS, MATERIAL_PRESETS, APP_CONFIG
from .control_panels import FileControlPanel, CameraControlPanel, MaterialControlPanel, ColorControlPanel
from .joint_controls import JointControlManager


class URDFIntegratedViewer(QMainWindow):
    """
    整合的URDF查看器 - 结合3D显示和智能关节控制

    这是一个完整的URDF机器人模型查看和交互系统，提供以下功能：
    - 3D可视化显示机器人模型
    - 智能关节分组控制（特别针对手指关节）
    - 实时运动学计算和更新
    - 材质和视觉效果控制
    - 预设动作和随机姿态生成

    主要特性：
    - 支持STL和其他3D网格格式
    - 自动处理ROS package路径
    - 优化的渲染性能
    - 直观的用户界面
    """

    def __init__(self):
        """
        初始化URDF整合查看器

        设置窗口属性、初始化所有必要的变量和组件，
        包括3D渲染引擎、关节控制系统、材质管理等。
        """
        super().__init__()
        self.setWindowTitle(APP_CONFIG['window_title'])
        self.setGeometry(100, 100, *APP_CONFIG['window_size'])

        # 核心数据变量
        self.robot = None                    # URDF机器人模型对象
        self.urdf_dir = None                 # URDF文件目录
        self.package_dir = None              # ROS包目录

        # 颜色设置
        self.link_color = DEFAULT_COLORS['link']
        self.joint_color = DEFAULT_COLORS['joint']

        # 关节控制相关
        self.joint_values = {}               # 当前关节值

        # 分析器和控制器实例
        self.joint_analyzer = None           # 关节分析器
        self.finger_group_analyzer = None    # 手指分组分析器

        # 材质属性管理
        self.current_material_type = "Default"
        self.material_properties = {
            "Metallic": {"roughness": 0.5, "metalness": 0.5},
            "Plastic": {"specular": 0.5},
            "Glass": {"transparency": 0.5}
        }

        # URDF材质信息
        self.urdf_materials = {}             # URDF中定义的材质
        self.link_materials = {}             # 每个链接的材质信息

        # 性能优化变量
        self.transform_calculator = None     # 变换计算器
        self.update_timer = QTimer()         # 更新定时器
        self.update_timer.setSingleShot(True)
        self.update_timer.setInterval(APP_CONFIG['update_interval'])
        self.update_timer.timeout.connect(self._update_view)
        self.pending_update = False          # 待更新标志

        # 线程相关变量
        self.load_worker = None              # URDF加载工作线程
        self.is_loading = False              # 是否正在加载标志

        # 初始化组件
        self.mesh_renderer = None
        self.camera_controller = None
        self.view_widget = None

        # 初始化用户界面
        self.setup_ui()

    def setup_ui(self):
        """
        设置用户界面

        创建主窗口布局，包括左侧控制面板和右侧3D视图区域。
        使用QSplitter实现可调整大小的分割布局。
        """
        # 创建主widget和布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)

        # 使用QSplitter创建可调整大小的水平分割布局
        main_splitter = QSplitter(Qt.Horizontal)
        main_widget_layout = QHBoxLayout(main_widget)
        main_widget_layout.addWidget(main_splitter)

        # 左侧控制面板
        control_panel = self.create_control_panel()
        main_splitter.addWidget(control_panel)

        # 右侧3D视图
        view_widget = self.create_3d_view()
        main_splitter.addWidget(view_widget)

        # 设置分割比例 (控制面板:3D视图 = 1:2)
        main_splitter.setSizes([500, 1000])

    def create_control_panel(self):
        """
        创建左侧控制面板

        控制面板包含所有的用户交互控件，使用滚动区域以适应不同的屏幕尺寸。
        面板内容包括：文件控制、相机控制、材质控制、颜色控制和关节控制。

        Returns:
            QScrollArea: 包含所有控制组件的滚动区域
        """
        # 创建滚动区域，确保在小屏幕上也能访问所有控件
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMinimumWidth(APP_CONFIG['control_panel_width'][0])
        scroll_area.setMaximumWidth(APP_CONFIG['control_panel_width'][1])

        # 控制面板主widget
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)

        # 文件控制组
        self.file_control = FileControlPanel()
        self.file_control.load_urdf_requested.connect(self.load_urdf)
        self.file_control.save_image_requested.connect(self.save_urdf_image)
        control_layout.addWidget(self.file_control)

        # 相机控制组
        self.camera_control = CameraControlPanel()
        self._connect_camera_signals()
        control_layout.addWidget(self.camera_control)

        # 材质控制组
        self.material_control = MaterialControlPanel()
        self._connect_material_signals()
        control_layout.addWidget(self.material_control)

        # 颜色控制组
        self.color_control = ColorControlPanel()
        self._connect_color_signals()
        control_layout.addWidget(self.color_control)

        # 关节控制组（将在加载URDF后动态创建）
        self.joint_control = JointControlManager()
        self._connect_joint_signals()
        control_layout.addWidget(self.joint_control)

        # 添加弹性空间，将控件推向顶部
        control_layout.addStretch()

        scroll_area.setWidget(control_widget)
        return scroll_area

    def _connect_camera_signals(self):
        """连接相机控制信号"""
        self.camera_control.distance_changed.connect(self.update_camera_distance)
        self.camera_control.azimuth_changed.connect(self.update_camera_azimuth)
        self.camera_control.elevation_changed.connect(self.update_camera_elevation)
        self.camera_control.center_x_changed.connect(self.update_camera_center_x)
        self.camera_control.center_y_changed.connect(self.update_camera_center_y)
        self.camera_control.center_z_changed.connect(self.update_camera_center_z)
        self.camera_control.reset_camera_requested.connect(self.reset_camera)
        self.camera_control.reset_center_requested.connect(self.reset_camera_center)
        self.camera_control.auto_center_requested.connect(self.auto_center_model)
        self.camera_control.preset_requested.connect(self.set_camera_preset)
        self.camera_control.test_requested.connect(self.on_test_button)

    def _connect_material_signals(self):
        """连接材质控制信号"""
        self.material_control.material_type_changed.connect(self.update_material_type)
        self.material_control.material_properties_changed.connect(self.update_material_properties)
        self.material_control.material_preset_applied.connect(self.apply_material_preset)
        self.material_control.edges_toggled.connect(self.toggle_edges)
        self.material_control.wireframe_toggled.connect(self.toggle_wireframe)
        self.material_control.grid_toggled.connect(self.toggle_grid)
        self.material_control.axis_toggled.connect(self.toggle_axis)

    def _connect_color_signals(self):
        """连接颜色控制信号"""
        self.color_control.link_color_changed.connect(self.choose_link_color)
        self.color_control.joint_color_changed.connect(self.choose_joint_color)

    def _connect_joint_signals(self):
        """连接关节控制信号"""
        self.joint_control.joint_value_changed.connect(self.update_joint_value)
        self.joint_control.finger_group_changed.connect(self.update_finger_group)
        self.joint_control.random_pose_requested.connect(self.apply_random_pose)
        self.joint_control.reset_pose_requested.connect(self.reset_pose)

    def create_3d_view(self):
        """
        创建3D视图组件

        使用PyQtGraph的OpenGL视图组件创建3D渲染窗口。
        设置合适的背景颜色和最小尺寸，为后续的网格和坐标轴显示做准备。

        Returns:
            gl.GLViewWidget: 3D视图组件
        """
        # 创建OpenGL 3D视图组件
        self.view_widget = gl.GLViewWidget()
        self.view_widget.setMinimumSize(*APP_CONFIG['min_3d_view_size'])

        # 设置专业的深色背景颜色，提供更好的视觉对比
        self.view_widget.setBackgroundColor(DEFAULT_COLORS['background'])

        # 初始化渲染器和相机控制器
        self.mesh_renderer = MeshRenderer(self.view_widget)
        self.camera_controller = CameraController(self.view_widget)

        # 初始化网格和坐标轴的引用变量，用于后续的显示控制
        self.grid_item = None    # 网格显示项
        self.axis_item = None    # 坐标轴显示项

        return self.view_widget

    def load_urdf(self):
        """
        加载URDF文件的主函数（多线程版本）

        使用后台线程执行URDF文件加载，避免阻塞主界面线程。
        主线程只负责文件选择和UI更新。
        """
        # 如果正在加载，防止重复加载
        if self.is_loading:
            self.file_control.set_status("正在加载中，请稍候...")
            return

        # 设置默认的URDF文件路径（相对于当前脚本位置）
        default_urdf_path = os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                       "..", "..", "Brain-righthand-URDF-V2", "urdf", "Brain-righthand-URDF-V2.urdf")

        # 打开文件选择对话框
        file_name, _ = QFileDialog.getOpenFileName(
            self, "打开URDF文件", default_urdf_path, "URDF Files (*.urdf)"
        )

        # 如果用户没有选择文件，使用默认路径
        if not file_name:
            file_name = default_urdf_path

        # 验证文件存在性并开始加载流程
        if file_name and os.path.exists(file_name):
            # 设置加载状态
            self.is_loading = True
            self.file_control.set_loading_state(True)
            self.file_control.set_status("正在加载URDF文件...")

            # 清除现有数据
            self.clear_existing_data()

            # 创建并启动工作线程
            workspace_root = os.getcwd()
            self.load_worker = URDFLoadWorker(file_name, workspace_root)

            # 连接信号
            self.load_worker.progress_updated.connect(self.on_loading_progress)
            self.load_worker.loading_finished.connect(self.on_loading_finished)
            self.load_worker.loading_failed.connect(self.on_loading_failed)

            # 启动线程
            self.load_worker.start()

            print(f"开始在后台线程中加载URDF文件: {file_name}")
        else:
            self.file_control.set_status("URDF文件不存在或未选择")

    @Slot(str)
    def on_loading_progress(self, message):
        """
        处理加载进度更新信号

        Args:
            message (str): 进度消息
        """
        self.file_control.set_status(message)
        print(message)

    @Slot(object, str, str)
    def on_loading_finished(self, robot, urdf_dir, package_dir):
        """
        处理加载完成信号

        Args:
            robot: URDF机器人模型对象
            urdf_dir (str): URDF文件目录
            package_dir (str): 包目录
        """
        try:
            # 保存加载结果
            self.robot = robot
            self.urdf_dir = urdf_dir
            self.package_dir = package_dir

            print(f"URDF目录: {self.urdf_dir}")
            print(f"包目录: {self.package_dir}")

            # 分析关节信息
            self.file_control.set_status("正在分析关节信息...")
            QApplication.processEvents()

            # 使用原始文件路径进行关节分析
            original_file = self.load_worker.file_path
            self.joint_analyzer = URDFJointAnalyzer(original_file)
            self.joint_analyzer.print_joint_info()

            # 检查是否找到可控关节
            if not self.joint_analyzer.get_controllable_joints():
                print("警告: 未找到可控关节！")
            else:
                # 创建手指分组分析器
                self.finger_group_analyzer = FingerGroupAnalyzer(self.joint_analyzer)

                # 调试输出：显示手指分组信息
                finger_groups = self.finger_group_analyzer.get_finger_groups()
                print("\n=== 手指分组调试信息 ===")
                print("手指组:")
                for group_key, group_config in finger_groups['finger_groups'].items():
                    print(f"  {group_key}: {group_config['joints']}")
                print("独立关节:")
                for joint_name in finger_groups['individual_joints'].keys():
                    print(f"  {joint_name}")
                print("========================\n")

            # 解析材质信息
            self.file_control.set_status("正在解析材质信息...")
            QApplication.processEvents()
            converted_urdf_path = original_file.replace('.urdf', '_converted.urdf')
            if os.path.exists(converted_urdf_path):
                self.parse_urdf_materials(converted_urdf_path)
            else:
                self.parse_urdf_materials(original_file)

            # 初始化运动学计算器
            self.file_control.set_status("正在初始化运动学计算器...")
            QApplication.processEvents()
            self.transform_calculator = SimpleTransformCalculator(self.robot)

            # 初始化关节值
            self.initialize_joint_values()

            # 加载和显示网格
            self.file_control.set_status("正在加载3D模型...")
            QApplication.processEvents()
            self.load_and_display_meshes()

            # 创建关节控制界面
            self.file_control.set_status("正在创建控制界面...")
            QApplication.processEvents()
            self.create_joint_controls()

            # 重置相机
            self.reset_camera()

            # 启用保存图片按钮
            self.file_control.enable_save_image(True)

            # 更新状态
            file_name = os.path.basename(original_file)
            self.file_control.set_status(f"已加载: {file_name}")

            print(f"URDF文件加载完成: {file_name}")

        except Exception as e:
            error_msg = f"处理加载结果时出错: {str(e)}"
            print(error_msg)
            import traceback
            print("错误追踪:")
            print(traceback.format_exc())
            self.file_control.set_status(error_msg)
        finally:
            # 恢复UI状态
            self.is_loading = False
            self.file_control.set_loading_state(False)
            self.load_worker = None

    @Slot(str)
    def on_loading_failed(self, error_message):
        """
        处理加载失败信号

        Args:
            error_message (str): 错误消息
        """
        self.file_control.set_status(f"加载失败: {error_message}")
        print(f"URDF加载失败: {error_message}")

        # 恢复UI状态
        self.is_loading = False
        self.file_control.set_loading_state(False)
        self.load_worker = None

    def clear_existing_data(self):
        """清除现有的数据和显示"""
        # 清除网格
        if self.mesh_renderer:
            self.mesh_renderer.clear_meshes()

        # 重置数据变量
        self.robot = None
        self.urdf_dir = None
        self.package_dir = None
        self.joint_values = {}
        self.urdf_materials = {}
        self.link_materials = {}

        # 重置分析器
        self.joint_analyzer = None
        self.finger_group_analyzer = None
        self.transform_calculator = None

        # 清除关节控制界面
        if hasattr(self, 'joint_control'):
            self.joint_control.clear_controls()

    def parse_urdf_materials(self, urdf_file_path):
        """
        解析URDF文件中的材质信息

        Args:
            urdf_file_path (str): URDF文件路径
        """
        try:
            tree = ET.parse(urdf_file_path)
            root = tree.getroot()

            # 解析全局材质定义
            for material in root.findall('material'):
                material_name = material.get('name')
                if material_name:
                    material_info = {}

                    # 解析颜色信息
                    color_elem = material.find('color')
                    if color_elem is not None:
                        rgba_str = color_elem.get('rgba', '0.5 0.5 0.5 1.0')
                        try:
                            rgba_values = [float(x) for x in rgba_str.split()]
                            if len(rgba_values) >= 3:
                                material_info['color'] = rgba_values
                        except ValueError:
                            print(f"无法解析材质 {material_name} 的颜色值: {rgba_str}")

                    # 解析纹理信息
                    texture_elem = material.find('texture')
                    if texture_elem is not None:
                        texture_filename = texture_elem.get('filename')
                        if texture_filename:
                            material_info['texture'] = texture_filename

                    self.urdf_materials[material_name] = material_info

            # 解析链接的材质引用
            for link in root.findall('link'):
                link_name = link.get('name')
                if link_name:
                    # 查找视觉元素的材质
                    for visual in link.findall('visual'):
                        material_elem = visual.find('material')
                        if material_elem is not None:
                            material_name = material_elem.get('name')
                            if material_name:
                                # 检查是否有内联颜色定义
                                color_elem = material_elem.find('color')
                                if color_elem is not None:
                                    rgba_str = color_elem.get('rgba', '0.5 0.5 0.5 1.0')
                                    try:
                                        rgba_values = [float(x) for x in rgba_str.split()]
                                        if len(rgba_values) >= 3:
                                            self.link_materials[link_name] = {
                                                'name': material_name,
                                                'color': rgba_values
                                            }
                                    except ValueError:
                                        print(f"无法解析链接 {link_name} 的颜色值: {rgba_str}")
                                else:
                                    # 只有材质名称引用
                                    self.link_materials[link_name] = {'name': material_name}

            print(f"解析到 {len(self.urdf_materials)} 个全局材质定义")
            print(f"解析到 {len(self.link_materials)} 个链接材质引用")

        except Exception as e:
            print(f"解析URDF材质信息时出错: {e}")

    def initialize_joint_values(self):
        """初始化关节值"""
        if self.joint_analyzer:
            controllable_joints = self.joint_analyzer.get_controllable_joints()
            for joint_name, joint_info in controllable_joints.items():
                # 将关节初始化为范围的中点
                lower = joint_info['lower']
                upper = joint_info['upper']
                if upper > lower:
                    initial_value = (lower + upper) / 2
                else:
                    initial_value = lower
                self.joint_values[joint_name] = initial_value

    def load_and_display_meshes(self):
        """
        加载并显示所有网格

        遍历机器人模型的所有链接，加载对应的3D网格文件，
        并在3D视图中显示。处理材质颜色和变换矩阵。
        """
        if not self.robot:
            return

        print("开始加载和显示网格...")

        # 获取所有链接的变换矩阵
        transforms = self.transform_calculator.get_all_transforms(self.joint_values)

        # 遍历所有链接
        for link in self.robot.links:
            link_name = link.name

            # 跳过没有视觉元素的链接
            if not link.visuals:
                continue

            print(f"处理链接: {link_name}")

            # 处理每个视觉元素
            for visual in link.visuals:
                if visual.geometry and hasattr(visual.geometry, 'filename'):
                    mesh_path = visual.geometry.filename

                    # 转换相对路径为绝对路径
                    if not os.path.isabs(mesh_path):
                        if mesh_path.startswith('package://'):
                            # 处理ROS包路径
                            if self.package_dir:
                                relative_path = mesh_path.replace('package://', '')
                                mesh_path = os.path.join(self.package_dir, relative_path)
                            else:
                                print(f"无法解析包路径: {mesh_path}")
                                continue
                        else:
                            # 相对于URDF文件的路径
                            mesh_path = os.path.join(self.urdf_dir, mesh_path)

                    # 加载网格文件
                    mesh_data = self.mesh_renderer.load_mesh_file(mesh_path)
                    if mesh_data is None:
                        print(f"无法加载网格文件: {mesh_path}")
                        continue

                    vertices, faces = mesh_data

                    # 获取网格颜色
                    color = self.mesh_renderer.get_mesh_color_from_material(
                        link_name, self.urdf_materials, self.link_materials, self.link_color
                    )

                    # 创建网格显示项
                    mesh_item = self.mesh_renderer.create_mesh_item(vertices, faces, color)
                    if mesh_item is None:
                        continue

                    # 计算最终变换矩阵
                    link_transform = transforms.get(link_name, np.eye(4))

                    # 应用视觉元素的局部变换
                    if visual.origin:
                        visual_transform = self.transform_calculator.origin_to_matrix(visual.origin)
                        final_transform = np.dot(link_transform, visual_transform)
                    else:
                        final_transform = link_transform

                    # 应用变换到网格
                    self.mesh_renderer.apply_transform_to_mesh(mesh_item, final_transform)

                    # 添加到场景
                    self.mesh_renderer.add_mesh_to_scene(mesh_item)

        print(f"网格加载完成，共显示 {len(self.mesh_renderer.mesh_items)} 个网格")

    def create_joint_controls(self):
        """创建关节控制界面"""
        if not self.finger_group_analyzer:
            print("手指分组分析器未初始化")
            return

        # 获取手指分组信息
        finger_groups = self.finger_group_analyzer.get_finger_groups()

        # 创建控制界面
        self.joint_control.create_joint_controls(finger_groups)

        print("关节控制界面创建完成")

    # 关节控制方法
    @Slot(str, float)
    def update_joint_value(self, joint_name, value):
        """
        更新单个关节值

        Args:
            joint_name (str): 关节名称
            value (float): 关节值
        """
        if joint_name in self.joint_values:
            self.joint_values[joint_name] = value
            self.schedule_update()

    @Slot(str, float)
    def update_finger_group(self, group_key, ratio):
        """
        更新手指分组

        Args:
            group_key (str): 分组键
            ratio (float): 比例值 (0.0-1.0)
        """
        if not self.finger_group_analyzer:
            return

        # 获取分组配置
        finger_groups = self.finger_group_analyzer.get_finger_groups()
        if group_key not in finger_groups['finger_groups']:
            return

        group_config = finger_groups['finger_groups'][group_key]
        joint_names = group_config['joints']

        # 更新分组中的所有关节
        for joint_name in joint_names:
            if joint_name in self.joint_values:
                joint_info = self.joint_analyzer.get_controllable_joints()[joint_name]
                lower = joint_info['lower']
                upper = joint_info['upper']

                # 计算新的关节值
                new_value = lower + (upper - lower) * ratio
                self.joint_values[joint_name] = new_value

        self.schedule_update()

    @Slot()
    def apply_random_pose(self):
        """应用随机姿态"""
        if not self.joint_analyzer:
            return

        # 为所有可控关节设置随机值
        controllable_joints = self.joint_analyzer.get_controllable_joints()
        for joint_name, joint_info in controllable_joints.items():
            lower = joint_info['lower']
            upper = joint_info['upper']
            if upper > lower:
                import random
                random_value = random.uniform(lower, upper)
                self.joint_values[joint_name] = random_value

        # 更新UI控件
        self.joint_control.apply_random_pose()

        self.schedule_update()

    @Slot()
    def reset_pose(self):
        """重置姿态"""
        if not self.joint_analyzer:
            return

        # 重置所有关节到中间位置
        controllable_joints = self.joint_analyzer.get_controllable_joints()
        for joint_name, joint_info in controllable_joints.items():
            lower = joint_info['lower']
            upper = joint_info['upper']
            if upper > lower:
                middle_value = (lower + upper) / 2
            else:
                middle_value = lower
            self.joint_values[joint_name] = middle_value

        # 更新UI控件
        self.joint_control.reset_all_joints()

        self.schedule_update()

    def schedule_update(self):
        """安排更新（防抖动）"""
        if not self.pending_update:
            self.pending_update = True
            self.update_timer.start()

    def _update_view(self):
        """更新3D视图"""
        if not self.transform_calculator or not self.mesh_renderer:
            self.pending_update = False
            return

        try:
            # 计算新的变换矩阵
            transforms = self.transform_calculator.get_all_transforms(self.joint_values)

            # 更新每个网格的变换
            link_mesh_map = {}  # 链接名到网格项的映射

            # 建立链接到网格的映射
            mesh_index = 0
            for link in self.robot.links:
                if not link.visuals:
                    continue

                for visual in link.visuals:
                    if visual.geometry and hasattr(visual.geometry, 'filename'):
                        if mesh_index < len(self.mesh_renderer.mesh_items):
                            if link.name not in link_mesh_map:
                                link_mesh_map[link.name] = []
                            link_mesh_map[link.name].append({
                                'mesh_item': self.mesh_renderer.mesh_items[mesh_index],
                                'visual': visual
                            })
                            mesh_index += 1

            # 更新变换
            for link_name, mesh_data_list in link_mesh_map.items():
                if link_name in transforms:
                    link_transform = transforms[link_name]

                    for mesh_data in mesh_data_list:
                        mesh_item = mesh_data['mesh_item']
                        visual = mesh_data['visual']

                        # 计算最终变换
                        if visual.origin:
                            visual_transform = self.transform_calculator.origin_to_matrix(visual.origin)
                            final_transform = np.dot(link_transform, visual_transform)
                        else:
                            final_transform = link_transform

                        # 应用变换
                        self.mesh_renderer.apply_transform_to_mesh(mesh_item, final_transform)

        except Exception as e:
            print(f"更新视图时出错: {e}")
        finally:
            self.pending_update = False

    # 相机控制方法
    @Slot(float)
    def update_camera_distance(self, distance):
        """更新相机距离"""
        if self.camera_controller:
            self.camera_controller.set_camera_parameters(distance=distance)

    @Slot(float)
    def update_camera_azimuth(self, azimuth):
        """更新相机方位角"""
        if self.camera_controller:
            self.camera_controller.set_camera_parameters(azimuth=azimuth)

    @Slot(float)
    def update_camera_elevation(self, elevation):
        """更新相机仰角"""
        if self.camera_controller:
            self.camera_controller.set_camera_parameters(elevation=elevation)

    @Slot(float)
    def update_camera_center_x(self, center_x):
        """更新相机中心X坐标"""
        if self.camera_controller:
            self.camera_controller.set_camera_parameters(center_x=center_x)

    @Slot(float)
    def update_camera_center_y(self, center_y):
        """更新相机中心Y坐标"""
        if self.camera_controller:
            self.camera_controller.set_camera_parameters(center_y=center_y)

    @Slot(float)
    def update_camera_center_z(self, center_z):
        """更新相机中心Z坐标"""
        if self.camera_controller:
            self.camera_controller.set_camera_parameters(center_z=center_z)

    @Slot()
    def reset_camera(self):
        """重置相机"""
        if self.camera_controller:
            self.camera_controller.reset_camera()

    @Slot()
    def reset_camera_center(self):
        """重置相机中心"""
        if self.camera_controller:
            self.camera_controller.set_camera_parameters(center_x=0, center_y=0, center_z=0)

    @Slot()
    def auto_center_model(self):
        """自动居中模型"""
        if self.camera_controller and self.mesh_renderer:
            self.camera_controller.auto_center_model(self.mesh_renderer.mesh_items)

    @Slot(str)
    def set_camera_preset(self, preset_name):
        """设置相机预设"""
        if self.camera_controller:
            self.camera_controller.set_camera_preset(preset_name)

    @Slot(float, float, float)
    def on_test_button(self, x, y, z):
        """测试按钮处理"""
        print(f"测试参数: x={x}, y={y}, z={z}")
        # 这里可以添加测试功能的具体实现

    # 材质和颜色控制方法
    @Slot(str)
    def update_material_type(self, material_type):
        """更新材质类型"""
        self.current_material_type = material_type
        self.apply_material_to_all_meshes()

    @Slot()
    def update_material_properties(self):
        """更新材质属性"""
        if not self.material_control:
            return

        # 获取当前材质属性
        properties = {
            'glossiness': self.material_control.glossiness_slider.value(),
            'metalness': self.material_control.metalness_slider.value(),
            'transparency': self.material_control.transparency_slider.value()
        }

        # 更新材质属性
        self.material_properties[self.current_material_type] = properties
        self.apply_material_to_all_meshes()

    @Slot(str)
    def apply_material_preset(self, preset_name):
        """应用材质预设"""
        if preset_name in MATERIAL_PRESETS:
            preset = MATERIAL_PRESETS[preset_name]

            # 更新UI控件
            if 'glossiness' in preset:
                self.material_control.glossiness_slider.setValue(preset['glossiness'])
            if 'metalness' in preset:
                self.material_control.metalness_slider.setValue(preset['metalness'])
            if 'transparency' in preset:
                self.material_control.transparency_slider.setValue(preset['transparency'])

            # 应用材质
            self.apply_material_to_all_meshes()

    def apply_material_to_all_meshes(self):
        """应用材质到所有网格"""
        if not self.mesh_renderer:
            return

        properties = self.material_properties.get(self.current_material_type, {})

        for mesh_item in self.mesh_renderer.mesh_items:
            self.mesh_renderer.update_mesh_material_properties(
                mesh_item, self.current_material_type, properties
            )

    @Slot(bool)
    def toggle_edges(self, show_edges):
        """切换边框显示"""
        # 这里可以实现边框显示逻辑
        print(f"切换边框显示: {show_edges}")

    @Slot(bool)
    def toggle_wireframe(self, wireframe):
        """切换线框模式"""
        if not self.mesh_renderer:
            return

        for mesh_item in self.mesh_renderer.mesh_items:
            self.mesh_renderer.toggle_wireframe_mode(mesh_item, wireframe)

    @Slot(bool)
    def toggle_grid(self, show_grid):
        """切换网格显示"""
        if show_grid:
            if self.grid_item is None:
                self.grid_item = gl.GLGridItem()
                self.grid_item.scale(1, 1, 1)
                self.view_widget.addItem(self.grid_item)
        else:
            if self.grid_item is not None:
                self.view_widget.removeItem(self.grid_item)
                self.grid_item = None

    @Slot(bool)
    def toggle_axis(self, show_axis):
        """切换坐标轴显示"""
        if show_axis:
            if self.axis_item is None:
                self.axis_item = gl.GLAxisItem()
                self.view_widget.addItem(self.axis_item)
        else:
            if self.axis_item is not None:
                self.view_widget.removeItem(self.axis_item)
                self.axis_item = None

    @Slot(QColor)
    def choose_link_color(self, color):
        """选择链接颜色"""
        self.link_color = (color.redF(), color.greenF(), color.blueF(), color.alphaF())
        self.update_all_mesh_colors()

    @Slot(QColor)
    def choose_joint_color(self, color):
        """选择关节颜色"""
        self.joint_color = (color.redF(), color.greenF(), color.blueF(), color.alphaF())
        # 这里可以更新关节显示颜色

    def update_all_mesh_colors(self):
        """更新所有网格颜色"""
        if not self.mesh_renderer:
            return

        for mesh_item in self.mesh_renderer.mesh_items:
            self.mesh_renderer.set_mesh_color(mesh_item, self.link_color)

    # 文件操作方法
    @Slot()
    def save_urdf_image(self):
        """保存URDF图片"""
        if not self.view_widget:
            return

        # 打开文件保存对话框
        file_name, _ = QFileDialog.getSaveFileName(
            self, "保存图片", "urdf_screenshot.png", "PNG Files (*.png);;JPG Files (*.jpg)"
        )

        if file_name:
            try:
                # 获取视图的图像
                image = self.view_widget.readQImage()
                if image.save(file_name):
                    self.file_control.set_status(f"图片已保存: {os.path.basename(file_name)}")
                    print(f"图片保存成功: {file_name}")
                else:
                    self.file_control.set_status("图片保存失败")
                    print("图片保存失败")
            except Exception as e:
                error_msg = f"保存图片时出错: {str(e)}"
                self.file_control.set_status(error_msg)
                print(error_msg)

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 停止加载线程
        if self.load_worker and self.load_worker.isRunning():
            self.load_worker.terminate()
            self.load_worker.wait()

        # 停止更新定时器
        if self.update_timer.isActive():
            self.update_timer.stop()

        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序属性
    app.setApplicationName("URDF整合查看器")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("URDF Tools")

    # 创建主窗口
    viewer = URDFIntegratedViewer()
    viewer.show()

    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
