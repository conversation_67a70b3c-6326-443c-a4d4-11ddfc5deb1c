# -*- coding: utf-8 -*-
"""
URDF整合查看器 - 3D机器人模型显示与关节控制系统

该模块提供了一个完整的URDF文件查看和交互系统，包括：
- URDF文件解析和路径转换
- 3D网格模型加载和显示
- 智能关节分组控制
- 实时运动学计算
- 材质和视觉效果控制

主要功能：
1. 支持ROS package路径自动转换
2. 手指关节智能分组联动控制
3. 实时3D可视化和交互
4. 预设动作和随机姿态生成
5. 材质和颜色自定义

系统架构：
┌─────────────────────────────────────────────────────────────┐
│                    URDF整合查看器                            │
├─────────────────────────────────────────────────────────────┤
│  UI层                                                       │
│  ├── 控制面板 (文件、相机、材质、颜色、关节控制)              │
│  └── 3D视图 (OpenGL渲染)                                    │
├─────────────────────────────────────────────────────────────┤
│  业务逻辑层                                                  │
│  ├── URDFPathConverter (路径转换)                           │
│  ├── URDFJointAnalyzer (关节分析)                          │
│  ├── FingerGroupAnalyzer (手指分组)                        │
│  └── SimpleTransformCalculator (运动学计算)                │
├─────────────────────────────────────────────────────────────┤
│  数据层                                                      │
│  ├── URDF模型数据                                           │
│  ├── 3D网格数据                                             │
│  ├── 关节状态数据                                           │
│  └── 材质和视觉数据                                         │
└─────────────────────────────────────────────────────────────┘

核心特性：
- 高性能：选择性网格更新、顶点缓存、异步渲染
- 智能控制：手指关节自动分组、联动比例控制
- 用户友好：直观的UI设计、实时反馈、预设动作
- 扩展性：模块化设计、易于添加新功能

作者: [您的名字]
版本: 1.0
日期: 2024
"""

import sys
import os
import numpy as np
import time
import trimesh
import pyqtgraph.opengl as gl
from urdf_parser_py.urdf import URDF
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QPushButton, QFileDialog, QLabel,
                             QSlider, QGroupBox, QColorDialog, QComboBox, QScrollArea,
                             QFrame, QSplitter, QTabWidget, QSpinBox, QDoubleSpinBox)
from PySide6.QtCore import Qt, QTimer, QThread, Signal, QObject, Slot, QMutex
from PySide6.QtGui import QColor, QScreen, QPainter, QBrush, QVector3D
import stl  # STL文件处理库
from concurrent.futures import ThreadPoolExecutor, as_completed
from functools import lru_cache
from queue import Queue
import threading
import xml.etree.ElementTree as ET
from typing import Dict, List, Tuple, Optional


class URDFLoadWorker(QThread):
    """
    URDF加载工作线程

    在后台线程中执行URDF文件的加载和解析操作，避免阻塞主界面线程。
    通过信号与主线程通信，报告加载进度和结果。
    """

    # 定义信号
    progress_updated = Signal(str)  # 进度更新信号
    loading_finished = Signal(object, str, str)  # 加载完成信号 (robot, urdf_dir, package_dir)
    loading_failed = Signal(str)  # 加载失败信号

    def __init__(self, file_path, workspace_root):
        """
        初始化工作线程

        Args:
            file_path (str): URDF文件路径
            workspace_root (str): 工作空间根目录
        """
        super().__init__()
        print(222, workspace_root)
        self.file_path = file_path
        self.workspace_root = workspace_root
        self.robot = None
        self.urdf_dir = None
        self.package_dir = None

    def run(self):
        """
        线程主执行函数

        在后台线程中执行URDF加载的所有耗时操作
        """
        try:
            self.progress_updated.emit("正在初始化路径转换器...")

            # 初始化路径转换器
            path_converter = URDFPathConverter(self.workspace_root)

            self.progress_updated.emit("正在检查URDF文件...")

            # 检查URDF文件是否包含需要转换的ROS package路径
            converted_urdf_path = self.file_path
            with open(self.file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if "package://" in content:
                    self.progress_updated.emit("检测到ROS package路径，正在转换...")
                    output_path = self.file_path.replace('.urdf', '_converted.urdf')
                    converted_urdf_path = path_converter.convert_urdf_file(self.file_path, output_path)

            # 存储重要的目录路径信息
            self.urdf_dir = os.path.dirname(os.path.abspath(self.file_path))
            self.package_dir = os.path.dirname(self.urdf_dir)

            self.progress_updated.emit("正在解析URDF文件...")

            # 解析URDF文件，创建机器人模型对象
            self.robot = URDF.from_xml_file(converted_urdf_path)

            self.progress_updated.emit(f"成功解析URDF文件。找到 {len(self.robot.links)} 个链接。")

            # 发送加载完成信号
            self.loading_finished.emit(self.robot, self.urdf_dir, self.package_dir)

        except Exception as e:
            error_msg = f"加载URDF文件失败: {str(e)}"
            print(error_msg)
            import traceback
            print("错误追踪:")
            print(traceback.format_exc())
            self.loading_failed.emit(error_msg)


class URDFPathConverter:
    """URDF路径转换器，处理ROS package路径到实际文件路径的转换"""

    def __init__(self, workspace_root: str):
        """
        初始化URDF路径转换器

        Args:
            workspace_root (str): 工作空间根目录路径
        """
        self.workspace_root = workspace_root

    def convert_package_path(self, package_path: str) -> str:
        """
        将ROS package路径转换为实际文件路径

        ROS中的package://路径是一种特殊的URI格式，需要转换为实际的文件系统路径

        Args:
            package_path (str): ROS package路径，格式如 package://package_name/path/to/file

        Returns:
            str: 转换后的实际文件路径

        Example:
            package://Brain-righthand-URDF-V2/meshes/base_link.STL
            -> workspace_root/Brain-righthand-URDF-V2/meshes/base_link.STL
        """
        if package_path.startswith("package://"):
            # 移除 package:// 前缀（10个字符）
            relative_path = package_path[10:]
            # 构建完整路径
            full_path = os.path.join(self.workspace_root, relative_path)
            return full_path.replace('\\', '/')  # 统一使用正斜杠，避免Windows路径问题
        return package_path

    def convert_urdf_file(self, input_urdf_path: str, output_urdf_path: str) -> str:
        """
        转换URDF文件中的所有package路径为实际路径

        遍历URDF文件中的所有mesh元素，将package://路径转换为实际文件路径
        这样可以确保URDF解析器能够正确找到网格文件

        Args:
            input_urdf_path (str): 输入URDF文件路径
            output_urdf_path (str): 输出URDF文件路径

        Returns:
            str: 转换后的URDF文件路径，如果转换失败则返回原路径
        """
        try:
            # 解析XML文档
            tree = ET.parse(input_urdf_path)
            root = tree.getroot()

            # 查找所有mesh元素并转换路径
            for mesh in root.iter('mesh'):
                filename = mesh.get('filename')
                if filename and filename.startswith('package://'):
                    new_path = self.convert_package_path(filename)
                    mesh.set('filename', new_path)
                    print(f"转换路径: {filename} -> {new_path}")

            # 保存转换后的文件
            tree.write(output_urdf_path, encoding='utf-8', xml_declaration=True)
            print(f"转换后的URDF文件保存到: {output_urdf_path}")
            return output_urdf_path

        except Exception as e:
            print(f"转换URDF文件时出错: {e}")
            return input_urdf_path


class URDFJointAnalyzer:
    """URDF关节分析器，提取可控关节信息"""

    def __init__(self, urdf_path: str):
        """
        初始化URDF关节分析器

        Args:
            urdf_path (str): URDF文件路径
        """
        self.urdf_path = urdf_path
        self.joints_info = {}  # 存储可控关节信息的字典
        self._analyze_joints()  # 立即分析关节信息

    def _analyze_joints(self):
        """
        分析URDF文件中的关节信息

        解析URDF文件，提取所有非固定关节的信息，包括：
        - 关节类型（revolute, prismatic等）
        - 运动范围限制
        - 最大力矩和速度

        只保留有运动能力的关节（effort > 0），过滤掉固定关节和无效关节
        """
        try:
            # 解析URDF XML文件
            tree = ET.parse(self.urdf_path)
            root = tree.getroot()

            # 遍历所有关节元素
            for joint in root.iter('joint'):
                joint_name = joint.get('name')
                joint_type = joint.get('type')

                # 跳过固定关节，因为它们不能运动
                if joint_type == 'fixed':
                    continue

                # 获取关节运动限制信息
                limit_elem = joint.find('limit')
                if limit_elem is not None:
                    # 解析关节限制参数
                    lower = float(limit_elem.get('lower', '0'))      # 下限角度/位置
                    upper = float(limit_elem.get('upper', '0'))      # 上限角度/位置
                    effort = float(limit_elem.get('effort', '0'))    # 最大力矩/力
                    velocity = float(limit_elem.get('velocity', '1')) # 最大速度

                    # 只包含有运动能力的关节（effort > 0表示可以施加力矩）
                    if effort > 0:
                        self.joints_info[joint_name] = {
                            'type': joint_type,
                            'lower': lower,
                            'upper': upper,
                            'effort': effort,
                            'velocity': velocity
                        }

        except Exception as e:
            print(f"分析关节信息时出错: {e}")

    def get_controllable_joints(self) -> Dict:
        """
        获取可控关节信息

        Returns:
            Dict: 包含所有可控关节信息的字典
                 键为关节名称，值为关节参数字典
        """
        return self.joints_info

    def print_joint_info(self):
        """
        打印关节信息到控制台

        以格式化的方式显示所有可控关节的详细信息，
        包括类型、运动范围、最大力矩和速度等参数
        """
        print("\n=== 可控关节信息 ===")
        for name, info in self.joints_info.items():
            print(f"关节: {name}")
            print(f"  类型: {info['type']}")
            print(f"  范围: [{info['lower']:.3f}, {info['upper']:.3f}]")
            print(f"  最大力矩: {info['effort']}")
            print(f"  最大速度: {info['velocity']}")
            print()


class FingerGroupAnalyzer:
    """手指关节分组分析器"""

    def __init__(self, joint_analyzer: URDFJointAnalyzer):
        """
        初始化手指关节分组分析器

        Args:
            joint_analyzer (URDFJointAnalyzer): 关节分析器实例
        """
        self.hand_type = "left" if "left" in joint_analyzer.urdf_path else "right"
        self.joint_analyzer = joint_analyzer
        self.finger_groups = self._analyze_finger_groups()

    def _analyze_finger_groups(self) -> Dict[str, Dict]:
        """
        分析并创建手指关节分组

        根据预定义的手指关节模式，将相关的关节组合成逻辑分组，
        实现手指的联动控制。每个分组包含多个关节和对应的联动比例。

        Returns:
            Dict[str, Dict]: 包含手指分组和独立关节的字典
                - finger_groups: 手指联动分组
                - individual_joints: 不属于任何分组的独立关节
        """
        joint_info = self.joint_analyzer.get_controllable_joints()

        # 定义手指关节分组模式
        # 每个分组定义了关节名称、联动比例和功能描述
        # 支持多种命名模式（原始和right_前缀）
        finger_patterns = {
            'thumb_rotation': {
                'name': '拇指转动',
                'joints': [f'{self.hand_type}_thumb_metacarpal_joint'],
                'ratios': [1.0],  # 独立控制拇指根部转动
                'description': '控制拇指左右转动（对掌运动）'
            },
            'thumb_bend': {
                'name': '拇指弯曲',
                'joints': [f'{self.hand_type}_thumb_proximal_joint',
                           f'{self.hand_type}_thumb_distal_joint'],
                'ratios': [1.0, 0.8],  # 拇指弯曲联动，远端关节运动幅度较小
                'description': '控制拇指弯曲捏合'
            },
            'index': {
                'name': '食指',
                'joints': [f'{self.hand_type}_index_proximal_joint',
                           f'{self.hand_type}_index_distal_joint',
                           f'{self.hand_type}_index_tip_joint'],
                'ratios': [1.0, 0.8, 0.6],  # 递减的联动比例，模拟自然弯曲
                'description': '控制食指弯曲'
            },
            'middle': {
                'name': '中指',
                'joints': [f'{self.hand_type}_middle_proximal_joint',
                           f'{self.hand_type}_middle_distal_joint',
                           f'{self.hand_type}_middle_tip_joint'],
                'ratios': [1.0, 0.8, 0.6],
                'description': '控制中指弯曲'
            },
            'ring': {
                'name': '无名指',
                'joints': [f'{self.hand_type}_ring_proximal_joint',
                           f'{self.hand_type}_ring_distal_joint',
                           f'{self.hand_type}_ring_tip_joint'],
                'ratios': [1.0, 0.8, 0.6],
                'description': '控制无名指弯曲'
            },
            'pinky': {
                'name': '小指',
                'joints': [f'{self.hand_type}_pinky_proximal_joint',
                           f'{self.hand_type}_pinky_distal_joint',
                           f'{self.hand_type}_pinky_tip_joint'],
                'ratios': [1.0, 0.8, 0.6],
                'description': '控制小指弯曲'
            }
        }

        # 检查哪些关节实际存在，并创建有效的分组
        # 只有在URDF中实际存在的关节才会被包含在分组中
        valid_groups = {}
        individual_joints = {}

        for group_key, group_config in finger_patterns.items():
            existing_joints = []
            existing_ratios = []

            # 检查分组中的每个关节是否在URDF中存在
            for i, joint_name in enumerate(group_config['joints']):
                if joint_name in joint_info:
                    existing_joints.append(joint_name)
                    existing_ratios.append(group_config['ratios'][i])

            # 如果至少有一个关节存在，就创建这个分组
            if len(existing_joints) >= 1:
                valid_groups[group_key] = {
                    'name': group_config['name'],
                    'joints': existing_joints,
                    'ratios': existing_ratios,
                    'joint_info': {joint: joint_info[joint] for joint in existing_joints},
                    'description': group_config.get('description', '')
                }

        # 收集所有已分组的关节
        all_finger_joints = set()
        for group in valid_groups.values():
            all_finger_joints.update(group['joints'])

        # 将不属于任何手指分组的关节归类为独立关节
        for joint_name, info in joint_info.items():
            if joint_name not in all_finger_joints and joint_name not in individual_joints:
                individual_joints[joint_name] = {
                    'name': joint_name,
                    'info': info
                }

        return {
            'finger_groups': valid_groups,      # 手指联动分组
            'individual_joints': individual_joints  # 独立控制关节
        }

    def get_finger_groups(self):
        """
        获取手指分组信息

        Returns:
            Dict: 包含finger_groups和individual_joints的字典
        """
        return self.finger_groups


class SimpleTransformCalculator:
    """简化的变换计算器 - 专注于正确性"""

    def __init__(self, robot):
        """
        初始化简化的变换计算器

        Args:
            robot: URDF机器人模型对象
        """
        self.robot = robot
        self.joint_values = {}      # 存储所有关节的当前值
        self.link_transforms = {}   # 存储所有链接的变换矩阵

        # 初始化所有非固定关节的值为0
        for joint in self.robot.joints:
            if joint.type != 'fixed':
                self.joint_values[joint.name] = 0.0

        # 计算初始变换矩阵
        self.calculate_all_transforms()

    def update_joint_value(self, joint_name: str, value: float):
        """
        更新关节值并重新计算变换

        当关节值发生变化时，需要重新计算所有受影响链接的变换矩阵

        Args:
            joint_name (str): 关节名称
            value (float): 新的关节值（角度或位移）

        Returns:
            List[str]: 受影响的链接名称列表
        """
        if joint_name in self.joint_values:
            self.joint_values[joint_name] = value
            # 重新计算所有变换（简单但可靠的方法）
            self.calculate_all_transforms()
            return self.get_affected_links(joint_name)
        return []

    def get_affected_links(self, joint_name: str):
        """
        获取受关节影响的链接

        当一个关节运动时，该关节的子链接及其所有后代链接都会受到影响

        Args:
            joint_name (str): 关节名称

        Returns:
            List[str]: 受影响的链接名称列表
        """
        affected = []
        joint = next((j for j in self.robot.joints if j.name == joint_name), None)
        if joint:
            affected.append(joint.child)
            # 递归查找所有子链接
            self._find_child_links(joint.child, affected)
        return affected

    def _find_child_links(self, parent_link: str, affected: list):
        """
        递归查找子链接

        在运动学树中，一个链接的运动会影响其所有子链接

        Args:
            parent_link (str): 父链接名称
            affected (list): 受影响链接列表（会被修改）
        """
        child_joints = [j for j in self.robot.joints if j.parent == parent_link]
        for child_joint in child_joints:
            affected.append(child_joint.child)
            self._find_child_links(child_joint.child, affected)

    def calculate_all_transforms(self):
        """
        计算所有链接的变换矩阵

        使用递归方法按照运动学树的依赖关系计算每个链接的变换矩阵。
        每个链接的变换是其父链接变换与连接关节变换的组合。
        """
        self.link_transforms = {}
        processed = set()  # 记录已处理的链接，避免重复计算

        def calculate_link_transform(link_name):
            """
            递归计算单个链接的变换矩阵

            Args:
                link_name (str): 链接名称

            Returns:
                np.ndarray: 4x4变换矩阵
            """
            if link_name in processed:
                return self.link_transforms.get(link_name, np.eye(4))

            # 查找连接到此链接的关节
            joint = next((j for j in self.robot.joints if j.child == link_name), None)
            if not joint:
                # 这是根链接（没有父关节的链接），使用单位矩阵
                transform = np.eye(4)
                self.link_transforms[link_name] = transform
                processed.add(link_name)
                return transform

            # 确保父链接的变换已经计算
            parent_transform = calculate_link_transform(joint.parent)

            # 计算当前关节的变换
            joint_transform = self.calculate_joint_transform(joint)

            # 组合父链接变换和关节变换
            link_transform = np.dot(parent_transform, joint_transform)
            self.link_transforms[link_name] = link_transform
            processed.add(link_name)
            return link_transform

        # 处理所有链接
        for link in self.robot.links:
            calculate_link_transform(link.name)

    def calculate_joint_transform(self, joint):
        """
        计算单个关节的变换矩阵

        关节变换由两部分组成：
        1. 关节原点变换（origin transform）：关节在父链接坐标系中的位置和姿态
        2. 关节运动变换（motion transform）：由关节值产生的运动变换

        Args:
            joint: URDF关节对象

        Returns:
            np.ndarray: 4x4变换矩阵
        """
        # 1. 计算关节原点变换
        if joint.origin is not None:
            xyz = np.array(joint.origin.xyz)  # 位置偏移
            rpy = np.array(joint.origin.rpy)  # 姿态（Roll-Pitch-Yaw）

            # 将RPY角度转换为旋转矩阵
            roll, pitch, yaw = rpy
            c_r, s_r = np.cos(roll), np.sin(roll)
            c_p, s_p = np.cos(pitch), np.sin(pitch)
            c_y, s_y = np.cos(yaw), np.sin(yaw)

            # 分别计算绕X、Y、Z轴的旋转矩阵
            Rx = np.array([[1, 0, 0], [0, c_r, -s_r], [0, s_r, c_r]])
            Ry = np.array([[c_p, 0, s_p], [0, 1, 0], [-s_p, 0, c_p]])
            Rz = np.array([[c_y, -s_y, 0], [s_y, c_y, 0], [0, 0, 1]])

            # 组合旋转矩阵（ZYX顺序）
            origin_transform = np.eye(4)
            origin_transform[:3, :3] = np.dot(np.dot(Rz, Ry), Rx)
            origin_transform[:3, 3] = xyz
        else:
            origin_transform = np.eye(4)

        # 2. 计算关节运动变换
        joint_value = self.joint_values.get(joint.name, 0.0)

        if joint.type == 'revolute':
            # 旋转关节：绕指定轴旋转
            axis = np.array(joint.axis)
            if np.linalg.norm(axis) > 0:
                axis = axis / np.linalg.norm(axis)  # 归一化轴向量

                # 使用Rodrigues公式计算绕任意轴的旋转矩阵
                c = np.cos(joint_value)
                s = np.sin(joint_value)
                t = 1 - c
                x, y, z = axis

                joint_rotation = np.array([
                    [t*x*x + c,    t*x*y - s*z,  t*x*z + s*y],
                    [t*x*y + s*z,  t*y*y + c,    t*y*z - s*x],
                    [t*x*z - s*y,  t*y*z + s*x,  t*z*z + c]
                ])

                motion_transform = np.eye(4)
                motion_transform[:3, :3] = joint_rotation
            else:
                motion_transform = np.eye(4)

        elif joint.type == 'prismatic':
            # 移动关节：沿指定轴平移
            axis = np.array(joint.axis)
            if np.linalg.norm(axis) > 0:
                axis = axis / np.linalg.norm(axis)  # 归一化轴向量
                translation = axis * joint_value    # 计算平移向量

                motion_transform = np.eye(4)
                motion_transform[:3, 3] = translation
            else:
                motion_transform = np.eye(4)
        else:
            # 其他类型关节（如fixed）不产生运动
            motion_transform = np.eye(4)

        # 3. 组合原点变换和运动变换
        return np.dot(origin_transform, motion_transform)

    def get_link_transform(self, link_name: str) -> np.ndarray:
        """
        获取指定链接的变换矩阵

        Args:
            link_name (str): 链接名称

        Returns:
            np.ndarray: 4x4变换矩阵，如果链接不存在则返回单位矩阵
        """
        return self.link_transforms.get(link_name, np.eye(4))


class URDFIntegratedViewer(QMainWindow):
    """
    整合的URDF查看器 - 结合3D显示和智能关节控制

    这是一个完整的URDF机器人模型查看和交互系统，提供以下功能：
    - 3D可视化显示机器人模型
    - 智能关节分组控制（特别针对手指关节）
    - 实时运动学计算和更新
    - 材质和视觉效果控制
    - 预设动作和随机姿态生成

    主要特性：
    - 支持STL和其他3D网格格式
    - 自动处理ROS package路径
    - 优化的渲染性能
    - 直观的用户界面
    """

    def __init__(self):
        """
        初始化URDF整合查看器

        设置窗口属性、初始化所有必要的变量和组件，
        包括3D渲染引擎、关节控制系统、材质管理等。
        """
        super().__init__()
        self.setWindowTitle("URDF整合查看器 - 显示与控制")
        self.setGeometry(100, 100, 1600, 1000)

        # 核心数据变量
        self.robot = None                    # URDF机器人模型对象
        self.mesh_items = []                 # 3D网格显示项列表
        self.joint_items = {}                # 关节显示项字典
        self.camera_distance = 10.0          # 相机距离
        self.camera_rotation = 0             # 相机旋转角度
        self.urdf_dir = None                 # URDF文件目录
        self.package_dir = None              # ROS包目录

        # 相机状态管理
        self.camera_state = {
            'distance': 10.0,
            'elevation': 30.0,
            'azimuth': 45.0,
            'center': [0, 0, 0]
        }
        self.view_update_blocked = False     # 防止视图更新冲突的标志

        # 颜色设置
        self.link_color = QColor(180, 180, 180, 255)  # 默认链接颜色（灰色）
        self.joint_color = QColor(255, 0, 0, 255)     # 默认关节颜色（红色）

        # 关节控制相关
        self.joint_sliders = {}              # 关节滑块控件字典
        self.joint_transforms = {}           # 关节变换矩阵缓存
        self.link_transforms = {}            # 链接变换矩阵缓存
        self.mesh_data = {}                  # 网格数据缓存
        self.joint_values = {}               # 当前关节值

        # 分析器和控制器实例
        self.joint_analyzer = None           # 关节分析器
        self.finger_group_analyzer = None    # 手指分组分析器
        self.path_converter = None           # 路径转换器

        # 材质属性管理
        self.current_material_type = "Default"
        self.material_properties = {
            "Metallic": {"roughness": 0.5, "metalness": 0.5},
            "Plastic": {"specular": 0.5},
            "Glass": {"transparency": 0.5}
        }

        # URDF材质信息
        self.urdf_materials = {}             # URDF中定义的材质
        self.link_materials = {}             # 每个链接的材质信息

        # 性能优化变量
        self.transform_calculator = None     # 变换计算器
        self.update_timer = QTimer()         # 更新定时器
        self.update_timer.setSingleShot(True)
        self.update_timer.setInterval(16)    # 60FPS更新频率
        self.update_timer.timeout.connect(self._update_view)
        self.pending_update = False          # 待更新标志
        self.vertex_cache = {}               # 顶点缓存
        self.visual_transforms = {}          # 视觉变换缓存

        # 手指组控制变量
        self.finger_group_sliders = {}       # 手指组滑块
        self.finger_group_vars = {}          # 手指组变量
        self.individual_joint_sliders = {}   # 独立关节滑块
        self.individual_joint_vars = {}      # 独立关节变量

        # 线程相关变量
        self.load_worker = None              # URDF加载工作线程
        self.is_loading = False              # 是否正在加载标志

        # 初始化用户界面
        self.setup_ui()

    def setup_ui(self):
        """
        设置用户界面

        创建主窗口布局，包括左侧控制面板和右侧3D视图区域。
        使用QSplitter实现可调整大小的分割布局。
        """
        # 创建主widget和布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)

        # 使用QSplitter创建可调整大小的水平分割布局
        main_splitter = QSplitter(Qt.Horizontal)
        main_widget_layout = QHBoxLayout(main_widget)
        main_widget_layout.addWidget(main_splitter)

        # 左侧控制面板
        control_panel = self.create_control_panel()
        main_splitter.addWidget(control_panel)

        # 右侧3D视图
        view_widget = self.create_3d_view()
        # view_widget.setFixedSize(400, 600)
        main_splitter.addWidget(view_widget)

        # 设置分割比例 (控制面板:3D视图 = 1:2)
        main_splitter.setSizes([500, 1000])

    def create_control_panel(self):
        """
        创建左侧控制面板

        控制面板包含所有的用户交互控件，使用滚动区域以适应不同的屏幕尺寸。
        面板内容包括：文件控制、相机控制、材质控制、颜色控制和关节控制。

        Returns:
            QScrollArea: 包含所有控制组件的滚动区域
        """
        # 创建滚动区域，确保在小屏幕上也能访问所有控件
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMinimumWidth(480)
        scroll_area.setMaximumWidth(600)

        # 控制面板主widget
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)

        # 文件控制组 - 用于加载URDF文件
        file_group = self.create_file_control_group()
        control_layout.addWidget(file_group)

        # 相机控制组 - 控制3D视图的相机参数
        camera_group = self.create_camera_control_group()
        control_layout.addWidget(camera_group)

        # 材质控制组 - 控制模型的材质和显示效果
        material_group = self.create_material_control_group()
        control_layout.addWidget(material_group)

        # 颜色控制组 - 自定义链接和关节颜色
        color_group = self.create_color_control_group()
        control_layout.addWidget(color_group)

        # 关节控制组（将在加载URDF后动态创建）
        self.joint_control_widget = QWidget()
        self.joint_control_layout = QVBoxLayout(self.joint_control_widget)
        control_layout.addWidget(self.joint_control_widget)

        # 添加弹性空间，将控件推向顶部
        control_layout.addStretch()

        scroll_area.setWidget(control_widget)
        return scroll_area

    def create_file_control_group(self):
        """
        创建文件控制组

        包含URDF文件加载按钮、图片保存按钮和状态显示标签。

        Returns:
            QGroupBox: 文件控制组件
        """
        file_group = QGroupBox("文件控制")
        file_layout = QVBoxLayout(file_group)

        # URDF文件加载按钮
        self.load_button = QPushButton("加载 URDF")
        self.load_button.clicked.connect(self.load_urdf)
        file_layout.addWidget(self.load_button)

        # 图片保存按钮
        self.save_image_button = QPushButton("保存图片")
        self.save_image_button.clicked.connect(self.save_urdf_image)
        self.save_image_button.setEnabled(False)  # 初始状态禁用，直到加载URDF
        file_layout.addWidget(self.save_image_button)

        # 状态显示标签
        self.status_label = QLabel("未加载URDF文件")
        file_layout.addWidget(self.status_label)

        return file_group

    def create_camera_control_group(self):
        """
        创建相机控制组

        提供3D视图相机的距离、旋转和中心位置控制。
        用户可以通过滑块调整观察角度、距离和模型显示中心。

        Returns:
            QGroupBox: 相机控制组件
        """
        camera_group = QGroupBox("相机控制")
        camera_layout = QVBoxLayout(camera_group)

        # 距离控制滑块
        distance_layout = QHBoxLayout()
        distance_layout.addWidget(QLabel("距离:"))
        self.distance_slider = QSlider(Qt.Horizontal)
        self.distance_slider.setMinimum(1)      # 最小距离0.1单位 (滑块值1对应0.1)
        self.distance_slider.setMaximum(1000)   # 最大距离100单位 (滑块值1000对应100)
        self.distance_slider.setValue(100)      # 默认距离10单位 (滑块值100对应10)
        self.distance_slider.valueChanged.connect(self.update_camera_distance)
        distance_layout.addWidget(self.distance_slider)
        self.distance_value_label = QLabel("10.0")
        distance_layout.addWidget(self.distance_value_label)
        camera_layout.addLayout(distance_layout)

        self.spibbox = QDoubleSpinBox()
        self.spibbox.setValue(0.18)
        self.spibbox.setRange(-99999, 9999)


        self.spibbox3 = QDoubleSpinBox()
        self.spibbox3.setValue(0)
        self.spibbox3.setRange(-99999, 9999)

        self.spibbox2 = QDoubleSpinBox()
        self.spibbox2.setValue(0.12)
        self.spibbox2.setRange(-99999, 9999)

        self.test_button = QPushButton("测试")
        self.test_button.clicked.connect(self.on_test_button)
        distance_layout.addWidget(self.spibbox)
        distance_layout.addWidget(self.spibbox3)
        distance_layout.addWidget(self.spibbox2)
        distance_layout.addWidget(self.test_button)

        # 方位角控制滑块 (水平旋转)
        azimuth_layout = QHBoxLayout()
        azimuth_layout.addWidget(QLabel("方位角:"))
        self.azimuth_slider = QSlider(Qt.Horizontal)
        self.azimuth_slider.setMinimum(0)       # 0度
        self.azimuth_slider.setMaximum(360)     # 360度
        self.azimuth_slider.setValue(45)        # 默认45度
        self.azimuth_slider.valueChanged.connect(self.update_camera_azimuth)
        azimuth_layout.addWidget(self.azimuth_slider)
        self.azimuth_value_label = QLabel("45°")
        azimuth_layout.addWidget(self.azimuth_value_label)
        camera_layout.addLayout(azimuth_layout)

        # 仰角控制滑块 (垂直旋转)
        elevation_layout = QHBoxLayout()
        elevation_layout.addWidget(QLabel("仰角:"))
        self.elevation_slider = QSlider(Qt.Horizontal)
        self.elevation_slider.setMinimum(-90)   # -90度
        self.elevation_slider.setMaximum(90)    # 90度
        self.elevation_slider.setValue(30)      # 默认30度
        self.elevation_slider.valueChanged.connect(self.update_camera_elevation)
        elevation_layout.addWidget(self.elevation_slider)
        self.elevation_value_label = QLabel("30°")
        elevation_layout.addWidget(self.elevation_value_label)
        camera_layout.addLayout(elevation_layout)

        # 显示中心位置控制
        center_group = QGroupBox("显示中心位置")
        center_layout = QVBoxLayout(center_group)

        # X轴中心控制
        center_x_layout = QHBoxLayout()
        center_x_layout.addWidget(QLabel("X轴:"))
        self.center_x_slider = QSlider(Qt.Horizontal)
        self.center_x_slider.setMinimum(-100)   # -10.0 单位
        self.center_x_slider.setMaximum(100)    # 10.0 单位
        self.center_x_slider.setValue(0)        # 默认0
        self.center_x_slider.valueChanged.connect(self.update_camera_center_x)
        center_x_layout.addWidget(self.center_x_slider)
        self.center_x_value_label = QLabel("0.0")
        center_x_layout.addWidget(self.center_x_value_label)
        center_layout.addLayout(center_x_layout)

        # Y轴中心控制
        center_y_layout = QHBoxLayout()
        center_y_layout.addWidget(QLabel("Y轴:"))
        self.center_y_slider = QSlider(Qt.Horizontal)
        self.center_y_slider.setMinimum(-100)   # -10.0 单位
        self.center_y_slider.setMaximum(100)    # 10.0 单位
        self.center_y_slider.setValue(0)        # 默认0
        self.center_y_slider.valueChanged.connect(self.update_camera_center_y)
        center_y_layout.addWidget(self.center_y_slider)
        self.center_y_value_label = QLabel("0.0")
        center_y_layout.addWidget(self.center_y_value_label)
        center_layout.addLayout(center_y_layout)

        # Z轴中心控制
        center_z_layout = QHBoxLayout()
        center_z_layout.addWidget(QLabel("Z轴:"))
        self.center_z_slider = QSlider(Qt.Horizontal)
        self.center_z_slider.setMinimum(-100)   # -10.0 单位
        self.center_z_slider.setMaximum(100)    # 10.0 单位
        self.center_z_slider.setValue(0)        # 默认0
        self.center_z_slider.valueChanged.connect(self.update_camera_center_z)
        center_z_layout.addWidget(self.center_z_slider)
        self.center_z_value_label = QLabel("0.0")
        center_z_layout.addWidget(self.center_z_value_label)
        center_layout.addLayout(center_z_layout)

        # 中心位置控制按钮
        center_btn_layout = QHBoxLayout()

        reset_center_btn = QPushButton("重置中心")
        reset_center_btn.clicked.connect(self.reset_camera_center)
        center_btn_layout.addWidget(reset_center_btn)

        auto_center_btn = QPushButton("自动居中")
        auto_center_btn.clicked.connect(self.auto_center_model)
        center_btn_layout.addWidget(auto_center_btn)

        center_layout.addLayout(center_btn_layout)
        camera_layout.addWidget(center_group)

        # 相机控制按钮
        control_btn_layout = QHBoxLayout()

        reset_camera_btn = QPushButton("重置相机")
        reset_camera_btn.clicked.connect(self.reset_camera)
        control_btn_layout.addWidget(reset_camera_btn)

        camera_layout.addLayout(control_btn_layout)

        # 相机预设按钮
        preset_group = QGroupBox("视角预设")
        preset_group_layout = QVBoxLayout(preset_group)

        preset_row1 = QHBoxLayout()
        front_btn = QPushButton("正面")
        front_btn.clicked.connect(lambda: self.set_camera_preset("front"))
        preset_row1.addWidget(front_btn)

        side_btn = QPushButton("侧面")
        side_btn.clicked.connect(lambda: self.set_camera_preset("side"))
        preset_row1.addWidget(side_btn)

        top_btn = QPushButton("顶部")
        top_btn.clicked.connect(lambda: self.set_camera_preset("top"))
        preset_row1.addWidget(top_btn)

        preset_group_layout.addLayout(preset_row1)

        preset_row2 = QHBoxLayout()
        iso_btn = QPushButton("等轴测")
        iso_btn.clicked.connect(lambda: self.set_camera_preset("isometric"))
        preset_row2.addWidget(iso_btn)

        bottom_btn = QPushButton("底部")
        bottom_btn.clicked.connect(lambda: self.set_camera_preset("bottom"))
        preset_row2.addWidget(bottom_btn)

        back_btn = QPushButton("背面")
        back_btn.clicked.connect(lambda: self.set_camera_preset("back"))
        preset_row2.addWidget(back_btn)

        preset_group_layout.addLayout(preset_row2)
        camera_layout.addWidget(preset_group)

        return camera_group

    def on_test_button(self):
        self.set_camera_parameters(distance=self.spibbox.value(),
                                   azimuth=0,
                                   elevation=0,
                                   center_x=0,
                                   center_y=self.spibbox3.value(),
                                   center_z=self.spibbox2.value())

        # 设置材质类型为Robot
        self.material_type_combo.setCurrentText("Robot")

        # 设置透明度为43%
        self.transparency_slider.setValue(43)

    def create_material_control_group(self):
        """创建材质控制组"""
        material_group = QGroupBox("材质控制")
        material_layout = QVBoxLayout(material_group)

        # 材质类型选择
        material_type_layout = QHBoxLayout()
        material_type_layout.addWidget(QLabel("材质类型:"))
        self.material_type_combo = QComboBox()
        self.material_type_combo.addItems([
            "Default", "Metallic", "Plastic", "Glass",
            "Matte", "Glossy", "Skin", "Robot"
        ])
        self.material_type_combo.currentTextChanged.connect(self.update_material_type)
        material_type_layout.addWidget(self.material_type_combo)
        material_layout.addLayout(material_type_layout)

        # 材质属性控制
        self.material_controls = {}

        # 光泽度控制
        glossiness_layout = QHBoxLayout()
        glossiness_layout.addWidget(QLabel("光泽度:"))
        self.glossiness_slider = QSlider(Qt.Horizontal)
        self.glossiness_slider.setMinimum(0)
        self.glossiness_slider.setMaximum(100)
        self.glossiness_slider.setValue(50)
        self.glossiness_slider.valueChanged.connect(self.update_material_properties)
        self.glossiness_label = QLabel("50%")
        glossiness_layout.addWidget(self.glossiness_slider)
        glossiness_layout.addWidget(self.glossiness_label)
        material_layout.addLayout(glossiness_layout)

        # 金属度控制
        metalness_layout = QHBoxLayout()
        metalness_layout.addWidget(QLabel("金属度:"))
        self.metalness_slider = QSlider(Qt.Horizontal)
        self.metalness_slider.setMinimum(0)
        self.metalness_slider.setMaximum(100)
        self.metalness_slider.setValue(0)
        self.metalness_slider.valueChanged.connect(self.update_material_properties)
        self.metalness_label = QLabel("0%")
        metalness_layout.addWidget(self.metalness_slider)
        metalness_layout.addWidget(self.metalness_label)
        material_layout.addLayout(metalness_layout)

        # 透明度控制
        transparency_layout = QHBoxLayout()
        transparency_layout.addWidget(QLabel("透明度:"))
        self.transparency_slider = QSlider(Qt.Horizontal)
        self.transparency_slider.setMinimum(0)
        self.transparency_slider.setMaximum(100)
        self.transparency_slider.setValue(0)
        self.transparency_slider.valueChanged.connect(self.update_material_properties)
        self.transparency_label = QLabel("0%")
        transparency_layout.addWidget(self.transparency_slider)
        transparency_layout.addWidget(self.transparency_label)
        material_layout.addLayout(transparency_layout)

        # 连接滑块标签更新
        self.glossiness_slider.valueChanged.connect(
            lambda v: self.glossiness_label.setText(f"{v}%"))
        self.metalness_slider.valueChanged.connect(
            lambda v: self.metalness_label.setText(f"{v}%"))
        self.transparency_slider.valueChanged.connect(
            lambda v: self.transparency_label.setText(f"{v}%"))

        # 材质预设按钮
        preset_layout = QHBoxLayout()
        preset_layout.addWidget(QLabel("预设:"))

        chrome_btn = QPushButton("铬合金")
        chrome_btn.clicked.connect(lambda: self.apply_material_preset("chrome"))
        preset_layout.addWidget(chrome_btn)

        gold_btn = QPushButton("黄金")
        gold_btn.clicked.connect(lambda: self.apply_material_preset("gold"))
        preset_layout.addWidget(gold_btn)

        plastic_btn = QPushButton("塑料")
        plastic_btn.clicked.connect(lambda: self.apply_material_preset("plastic"))
        preset_layout.addWidget(plastic_btn)

        material_layout.addLayout(preset_layout)

        # 显示选项
        display_layout = QVBoxLayout()
        display_layout.addWidget(QLabel("显示选项:"))

        # 第一行：边框和线框
        display_row1 = QHBoxLayout()
        self.show_edges_checkbox = QPushButton("显示边框")
        self.show_edges_checkbox.setCheckable(True)
        self.show_edges_checkbox.setChecked(False)
        self.show_edges_checkbox.clicked.connect(self.toggle_edges)
        display_row1.addWidget(self.show_edges_checkbox)

        self.wireframe_checkbox = QPushButton("线框模式")
        self.wireframe_checkbox.setCheckable(True)
        self.wireframe_checkbox.setChecked(False)
        self.wireframe_checkbox.clicked.connect(self.toggle_wireframe)
        display_row1.addWidget(self.wireframe_checkbox)
        display_layout.addLayout(display_row1)

        # 第二行：网格和坐标轴
        display_row2 = QHBoxLayout()
        self.show_grid_checkbox = QPushButton("显示网格")
        self.show_grid_checkbox.setCheckable(True)
        self.show_grid_checkbox.setChecked(False)
        self.show_grid_checkbox.clicked.connect(self.toggle_grid)
        display_row2.addWidget(self.show_grid_checkbox)

        self.show_axis_checkbox = QPushButton("显示坐标轴")
        self.show_axis_checkbox.setCheckable(True)
        self.show_axis_checkbox.setChecked(False)
        self.show_axis_checkbox.clicked.connect(self.toggle_axis)
        display_row2.addWidget(self.show_axis_checkbox)
        display_layout.addLayout(display_row2)

        material_layout.addLayout(display_layout)

        return material_group

    def create_color_control_group(self):
        """创建颜色控制组"""
        color_group = QGroupBox("颜色控制")
        color_layout = QVBoxLayout(color_group)

        # 链接颜色
        link_color_layout = QHBoxLayout()
        link_color_layout.addWidget(QLabel("链接颜色:"))
        self.link_color_button = QPushButton("选择")
        self.link_color_button.clicked.connect(self.choose_link_color)
        link_color_layout.addWidget(self.link_color_button)
        color_layout.addLayout(link_color_layout)

        # 关节颜色
        joint_color_layout = QHBoxLayout()
        joint_color_layout.addWidget(QLabel("关节颜色:"))
        self.joint_color_button = QPushButton("选择")
        self.joint_color_button.clicked.connect(self.choose_joint_color)
        joint_color_layout.addWidget(self.joint_color_button)
        color_layout.addLayout(joint_color_layout)

        return color_group

    def create_3d_view(self):
        """
        创建3D视图组件

        使用PyQtGraph的OpenGL视图组件创建3D渲染窗口。
        设置合适的背景颜色和最小尺寸，为后续的网格和坐标轴显示做准备。

        Returns:
            gl.GLViewWidget: 3D视图组件
        """
        # 创建OpenGL 3D视图组件
        self.view_widget = gl.GLViewWidget()
        self.view_widget.setMinimumSize(800, 600)  # 设置最小尺寸确保良好的显示效果

        # 设置专业的深色背景颜色，提供更好的视觉对比
        self.view_widget.setBackgroundColor((0.15, 0.15, 0.2, 1.0))  # 深蓝灰色背景

        # 禁用鼠标操作 - 重写鼠标事件方法
        # self.view_widget.wheelEvent = lambda event: None

        # 初始化网格和坐标轴的引用变量，用于后续的显示控制
        self.grid_item = None    # 网格显示项
        self.axis_item = None    # 坐标轴显示项

        return self.view_widget

    def load_urdf(self):
        """
        加载URDF文件的主函数（多线程版本）

        使用后台线程执行URDF文件加载，避免阻塞主界面线程。
        主线程只负责文件选择和UI更新。
        """
        # 如果正在加载，防止重复加载
        if self.is_loading:
            self.status_label.setText("正在加载中，请稍候...")
            return

        # 设置默认的URDF文件路径（相对于当前脚本位置）
        default_urdf_path = os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                       "Brain-righthand-URDF-V2", "urdf", "Brain-righthand-URDF-V2.urdf")

        # 打开文件选择对话框
        file_name, _ = QFileDialog.getOpenFileName(
            self, "打开URDF文件", default_urdf_path, "URDF Files (*.urdf)"
        )

        # 如果用户没有选择文件，使用默认路径
        if not file_name:
            file_name = default_urdf_path

        # 验证文件存在性并开始加载流程
        if file_name and os.path.exists(file_name):
            # 设置加载状态
            self.is_loading = True
            self.load_button.setEnabled(False)
            self.status_label.setText("正在加载URDF文件...")

            # 清除现有数据
            self.clear_existing_data()

            # 创建并启动工作线程
            workspace_root = os.getcwd()
            self.load_worker = URDFLoadWorker(file_name, workspace_root)

            # 连接信号
            self.load_worker.progress_updated.connect(self.on_loading_progress)
            self.load_worker.loading_finished.connect(self.on_loading_finished)
            self.load_worker.loading_failed.connect(self.on_loading_failed)

            # 启动线程
            self.load_worker.start()

            print(f"开始在后台线程中加载URDF文件: {file_name}")
        else:
            self.status_label.setText("URDF文件不存在或未选择")

    @Slot(str)
    def on_loading_progress(self, message):
        """
        处理加载进度更新信号

        Args:
            message (str): 进度消息
        """
        self.status_label.setText(message)
        print(message)

    @Slot(object, str, str)
    def on_loading_finished(self, robot, urdf_dir, package_dir):
        """
        处理加载完成信号

        Args:
            robot: URDF机器人模型对象
            urdf_dir (str): URDF文件目录
            package_dir (str): 包目录
        """
        try:
            # 保存加载结果
            self.robot = robot
            self.urdf_dir = urdf_dir
            self.package_dir = package_dir

            print(11111, self.urdf_dir)
            print(11111, self.package_dir)

            # 分析关节信息
            self.status_label.setText("正在分析关节信息...")
            QApplication.processEvents()

            # 使用原始文件路径进行关节分析
            original_file = self.load_worker.file_path
            self.joint_analyzer = URDFJointAnalyzer(original_file)
            self.joint_analyzer.print_joint_info()

            # 检查是否找到可控关节
            if not self.joint_analyzer.get_controllable_joints():
                print("警告: 未找到可控关节！")
            else:
                # 创建手指分组分析器
                self.finger_group_analyzer = FingerGroupAnalyzer(self.joint_analyzer)

                # 调试输出：显示手指分组信息
                finger_groups = self.finger_group_analyzer.get_finger_groups()
                print("\n=== 手指分组调试信息 ===")
                print("手指组:")
                for group_key, group_config in finger_groups['finger_groups'].items():
                    print(f"  {group_key}: {group_config['joints']}")
                print("独立关节:")
                for joint_name in finger_groups['individual_joints'].keys():
                    print(f"  {joint_name}")
                print("========================\n")

            # 解析材质信息
            self.status_label.setText("正在解析材质信息...")
            QApplication.processEvents()
            converted_urdf_path = original_file.replace('.urdf', '_converted.urdf')
            if os.path.exists(converted_urdf_path):
                self.parse_urdf_materials(converted_urdf_path)
            else:
                self.parse_urdf_materials(original_file)

            # 初始化运动学计算器
            self.status_label.setText("正在初始化运动学计算器...")
            QApplication.processEvents()
            self.transform_calculator = SimpleTransformCalculator(self.robot)

            # 初始化关节值
            self.initialize_joint_values()

            # 预计算视觉变换
            self.precompute_visual_transforms()

            # 加载和显示网格
            self.status_label.setText("正在加载3D模型...")
            QApplication.processEvents()
            self.load_and_display_meshes()

            # 创建关节控制界面
            self.status_label.setText("正在创建控制界面...")
            QApplication.processEvents()
            self.create_joint_controls()

            # 重置相机
            self.reset_camera()

            # 启用保存图片按钮
            self.save_image_button.setEnabled(True)

            # 更新状态
            file_name = os.path.basename(original_file)
            self.status_label.setText(f"已加载: {file_name}")

            print(f"URDF文件加载完成: {file_name}")

        except Exception as e:
            error_msg = f"处理加载结果时出错: {str(e)}"
            print(error_msg)
            import traceback
            print("错误追踪:")
            print(traceback.format_exc())
            self.status_label.setText(error_msg)
        finally:
            # 恢复UI状态
            self.is_loading = False
            self.load_button.setEnabled(True)
            self.load_worker = None

    @Slot(str)
    def on_loading_failed(self, error_message):
        """
        处理加载失败信号

        Args:
            error_message (str): 错误消息
        """
        self.status_label.setText(f"加载失败: {error_message}")
        print(f"URDF加载失败: {error_message}")

        # 恢复UI状态
        self.is_loading = False
        self.load_button.setEnabled(True)
        self.load_worker = None

    def parse_urdf_materials(self, urdf_path):
        """解析URDF文件中的材质信息"""
        try:
            tree = ET.parse(urdf_path)
            root = tree.getroot()

            # 解析材质定义
            for material in root.iter('material'):
                material_name = material.get('name', '')
                if material_name:
                    color_elem = material.find('color')
                    if color_elem is not None:
                        rgba = color_elem.get('rgba', '1 1 1 1').split()
                        if len(rgba) == 4:
                            self.urdf_materials[material_name] = {
                                'color': [float(x) for x in rgba]
                            }

            # 解析每个链接的材质
            for link in root.iter('link'):
                link_name = link.get('name')
                visual = link.find('visual')
                if visual is not None:
                    material = visual.find('material')
                    if material is not None:
                        material_name = material.get('name', '')
                        color_elem = material.find('color')
                        if color_elem is not None:
                            rgba = color_elem.get('rgba', '1 1 1 1').split()
                            if len(rgba) == 4:
                                self.link_materials[link_name] = {
                                    'name': material_name,
                                    'color': [float(x) for x in rgba]
                                }

            print(f"解析到 {len(self.urdf_materials)} 个材质定义")
            print(f"解析到 {len(self.link_materials)} 个链接材质")

        except Exception as e:
            print(f"解析材质信息错误: {e}")
            self.urdf_materials = {}
            self.link_materials = {}

    def clear_existing_data(self):
        """清除现有数据"""
        # 清除现有网格
        for item in self.mesh_items:
            self.view_widget.removeItem(item)
        self.mesh_items = []
        self.joint_items = {}

        # 清除关节控制
        while self.joint_control_layout.count():
            item = self.joint_control_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        # 重置变换
        self.joint_transforms = {}
        self.link_transforms = {}
        self.mesh_data = {}
        self.joint_values = {}

        # 清除控制变量
        self.finger_group_sliders = {}
        self.finger_group_vars = {}
        self.individual_joint_sliders = {}
        self.individual_joint_vars = {}

        # 禁用保存图片按钮
        if hasattr(self, 'save_image_button'):
            self.save_image_button.setEnabled(False)

    def initialize_joint_values(self):
        """初始化关节值"""
        self.joint_values = {}
        for joint in self.robot.joints:
            if joint.type != 'fixed':
                # 设置初始关节值
                if joint.limit is not None:
                    # 对于旋转关节，设置到范围中间
                    if joint.type == 'revolute':
                        self.joint_values[joint.name] = (joint.limit.lower + joint.limit.upper) / 2
                    # 对于移动关节，设置到下限
                    elif joint.type == 'prismatic':
                        self.joint_values[joint.name] = joint.limit.lower
                else:
                    self.joint_values[joint.name] = 0.0

        # 将关节值同步到变换计算器
        if self.transform_calculator:
            for joint_name, value in self.joint_values.items():
                self.transform_calculator.joint_values[joint_name] = value
            self.transform_calculator.calculate_all_transforms()

    def precompute_visual_transforms(self):
        """预计算视觉变换"""
        self.visual_transforms = {}
        for link in self.robot.links:
            if link.visual and link.visual.origin:
                try:
                    xyz = np.array(link.visual.origin.xyz)
                    rpy = np.array(link.visual.origin.rpy)

                    # 创建视觉变换矩阵
                    roll, pitch, yaw = rpy
                    c_r, s_r = np.cos(roll), np.sin(roll)
                    c_p, s_p = np.cos(pitch), np.sin(pitch)
                    c_y, s_y = np.cos(yaw), np.sin(yaw)

                    Rx = np.array([[1, 0, 0], [0, c_r, -s_r], [0, s_r, c_r]])
                    Ry = np.array([[c_p, 0, s_p], [0, 1, 0], [-s_p, 0, c_p]])
                    Rz = np.array([[c_y, -s_y, 0], [s_y, c_y, 0], [0, 0, 1]])
                    R = np.dot(np.dot(Rz, Ry), Rx)

                    visual_transform = np.eye(4)
                    visual_transform[:3, :3] = R
                    visual_transform[:3, 3] = xyz

                    self.visual_transforms[link.name] = visual_transform
                except Exception as e:
                    print(f"预计算视觉变换错误 {link.name}: {e}")
                    self.visual_transforms[link.name] = np.eye(4)
            else:
                self.visual_transforms[link.name] = np.eye(4)

    def load_mesh_file(self, mesh_file):
        """
        加载3D网格文件

        支持多种3D文件格式，优先使用trimesh库，如果失败则回退到numpy-stl。
        这种双重策略确保了对不同格式文件的兼容性。

        Args:
            mesh_file (str): 网格文件路径

        Returns:
            tuple: (vertices, faces) 顶点数组和面数组，失败时返回(None, None)
        """
        try:
            # 策略1：优先使用trimesh库，支持多种格式（STL, OBJ, PLY等）
            try:
                mesh = trimesh.load(mesh_file)
                return mesh.vertices, mesh.faces
            except:
                # 策略2：如果trimesh失败，对STL文件使用numpy-stl库
                if mesh_file.lower().endswith('.stl'):
                    mesh = stl.mesh.Mesh.from_file(mesh_file)
                    # 将三角形向量重塑为顶点数组
                    vertices = mesh.vectors.reshape(-1, 3)
                    # 为每个顶点创建面索引
                    faces = np.arange(len(vertices)).reshape(-1, 3)
                    return vertices, faces
                else:
                    # 不支持的文件格式
                    raise
        except Exception as e:
            print(f"加载网格错误 {mesh_file}: {str(e)}")
            return None, None

    def load_and_display_meshes(self):
        """加载和显示网格"""
        # 首先计算所有链接的初始变换
        link_transforms = {}

        def calculate_link_transform(link_name):
            if link_name in link_transforms:
                return link_transforms[link_name]

            # 查找连接到此链接的关节
            joint = next((j for j in self.robot.joints if j.child == link_name), None)
            if not joint:
                # 如果没有找到关节，这是base_link或固定链接
                transform = np.eye(4)
                link_transforms[link_name] = transform
                return transform

            # 获取父变换
            parent_transform = calculate_link_transform(joint.parent)

            # 计算关节变换
            if joint.origin is not None:
                xyz = joint.origin.xyz
                rpy = joint.origin.rpy

                # 将RPY转换为旋转矩阵
                roll, pitch, yaw = rpy
                Rx = np.array([[1, 0, 0],
                             [0, np.cos(roll), -np.sin(roll)],
                             [0, np.sin(roll), np.cos(roll)]])
                Ry = np.array([[np.cos(pitch), 0, np.sin(pitch)],
                             [0, 1, 0],
                             [-np.sin(pitch), 0, np.cos(pitch)]])
                Rz = np.array([[np.cos(yaw), -np.sin(yaw), 0],
                             [np.sin(yaw), np.cos(yaw), 0],
                             [0, 0, 1]])
                origin_rotation = np.dot(np.dot(Rz, Ry), Rx)
            else:
                xyz = np.zeros(3)
                origin_rotation = np.eye(3)

            # 获取关节值
            joint_value = self.joint_values.get(joint.name, 0.0)

            # 创建关节旋转矩阵
            axis = np.array(joint.axis)
            axis = axis / np.linalg.norm(axis)

            c = np.cos(joint_value)
            s = np.sin(joint_value)
            t = 1 - c
            x, y, z = axis

            joint_rotation = np.array([
                [t*x*x + c,    t*x*y - s*z,  t*x*z + s*y],
                [t*x*y + s*z,  t*y*y + c,    t*y*z - s*x],
                [t*x*z - s*y,  t*y*z + s*x,  t*z*z + c]
            ])

            # 创建完整的关节变换
            joint_transform = np.eye(4)
            joint_transform[:3, :3] = np.dot(np.dot(origin_rotation, joint_rotation), origin_rotation.T)
            joint_transform[:3, 3] = xyz

            # 计算链接变换
            link_transform = np.dot(parent_transform, joint_transform)
            link_transforms[link_name] = link_transform
            return link_transform

        # 计算所有链接的变换，从根链接开始
        # 查找根链接（没有父关节的链接）
        root_links = []
        for link in self.robot.links:
            has_parent_joint = any(joint.child == link.name for joint in self.robot.joints)
            if not has_parent_joint:
                root_links.append(link.name)

        # 首先计算根链接的变换
        for root_link_name in root_links:
            calculate_link_transform(root_link_name)
            print(f"找到根链接: {root_link_name}")

        # 然后计算所有其他链接的变换
        for link in self.robot.links:
            if link.name not in root_links:
                calculate_link_transform(link.name)

        # 加载和变换网格
        for link in self.robot.links:
            if link.visual is not None:
                geometry = link.visual.geometry

                if hasattr(geometry, 'filename'):
                    mesh_file = geometry.filename

                    # 处理package://路径
                    if mesh_file.startswith('package://'):
                        parts = mesh_file[10:].split('/', 1)
                        if len(parts) == 2:
                            package_name, rel_path = parts
                            rel_path = rel_path.replace('\\', '/')
                            # 在工作空间中查找包
                            workspace_root = os.getcwd()
                            package_path = os.path.join(workspace_root, package_name)
                            mesh_file = os.path.join(package_path, *rel_path.split('/'))
                            print(f"转换package路径: {mesh_file}")

                    # 处理相对路径
                    if not os.path.isabs(mesh_file):
                        mesh_file = os.path.join(self.urdf_dir, mesh_file)

                    mesh_file = os.path.normpath(mesh_file)

                    print(f"尝试加载网格: {mesh_file}")

                    # 检查文件是否存在
                    if not os.path.exists(mesh_file):
                        print(f"警告: 网格文件不存在: {mesh_file}")
                        print(f"链接 {link.name} 的网格将不会显示")
                        continue

                    vertices, faces = self.load_mesh_file(mesh_file)

                    if vertices is not None and faces is not None:
                        # 存储原始网格数据
                        self.mesh_data[link.name] = {
                            'vertices': vertices,
                            'faces': faces
                        }

                        # 获取此链接的初始变换
                        if link.name not in link_transforms:
                            raise KeyError(f"链接 '{link.name}' 的变换缺失。")
                        transform = link_transforms[link.name]

                        # 如果指定了视觉原点变换，则应用它
                        if link.visual is not None and link.visual.origin is not None:
                            xyz = link.visual.origin.xyz
                            rpy = link.visual.origin.rpy
                            if None not in (xyz, rpy):
                                # 将RPY转换为旋转矩阵
                                roll, pitch, yaw = rpy
                                Rx = np.array([[1, 0, 0],
                                             [0, np.cos(roll), -np.sin(roll)],
                                             [0, np.sin(roll), np.cos(roll)]])
                                Ry = np.array([[np.cos(pitch), 0, np.sin(pitch)],
                                             [0, 1, 0],
                                             [-np.sin(pitch), 0, np.cos(pitch)]])
                                Rz = np.array([[np.cos(yaw), -np.sin(yaw), 0],
                                             [np.sin(yaw), np.cos(yaw), 0],
                                             [0, 0, 1]])
                                R = np.dot(np.dot(Rz, Ry), Rx)

                                # 创建并应用视觉变换
                                visual_transform = np.eye(4)
                                visual_transform[:3, :3] = R
                                visual_transform[:3, 3] = xyz
                                transform = np.dot(transform, visual_transform)

                        # 将变换应用到顶点
                        vertices_homogeneous = np.hstack((vertices, np.ones((vertices.shape[0], 1))))
                        vertices_transformed = np.dot(vertices_homogeneous, transform.T)
                        vertices = vertices_transformed[:, :3]

                        # 创建具有改进材质属性的网格项
                        mesh_item = gl.GLMeshItem(
                            vertexes=vertices,
                            faces=faces,
                            smooth=True,
                            drawEdges=False,  # 默认不显示边框，提高视觉效果
                            edgeColor=(0.2, 0.2, 0.2, 0.5),  # 更柔和的边框颜色
                            shader='shaded',
                            glOptions='opaque'
                        )

                        # 设置链接名称
                        mesh_item.link_name = link.name

                        # 根据URDF材质信息设置初始颜色
                        self.apply_link_material(mesh_item, link.name)

                        # 将网格添加到场景
                        self.view_widget.addItem(mesh_item)
                        self.mesh_items.append(mesh_item)
                        print(f"成功加载链接: {link.name}")
                    else:
                        print(f"警告: 无法加载链接 {link.name} 的网格数据")

        # 显示加载统计信息
        total_links = len(self.robot.links)
        loaded_meshes = len(self.mesh_items)
        print(f"\n=== 网格加载统计 ===")
        print(f"总链接数: {total_links}")
        print(f"成功加载的网格: {loaded_meshes}")
        print(f"未加载的链接: {total_links - loaded_meshes}")
        if total_links - loaded_meshes > 0:
            print("未加载的链接可能是因为:")
            print("1. 网格文件不存在")
            print("2. 文件路径错误")
            print("3. 文件格式不支持")
        print("==================\n")

    def create_joint_controls(self):
        """
        创建关节控制界面

        根据手指分组分析结果，动态创建用户界面控件。
        界面分为三个主要部分：
        1. 手指联动控制 - 多个关节协调运动
        2. 独立关节控制 - 单个关节独立控制
        3. 预设动作控制 - 常用手势和动作
        """
        # 检查分析器是否已初始化
        if not self.finger_group_analyzer:
            return

        finger_groups = self.finger_group_analyzer.get_finger_groups()

        # 第一部分：创建手指联动控制组
        finger_groups_data = finger_groups['finger_groups']
        if finger_groups_data:
            finger_control_group = QGroupBox("手指联动控制")
            finger_control_layout = QVBoxLayout(finger_control_group)

            # 为每个手指分组创建控制组件
            for group_key, group_config in finger_groups_data.items():
                group_widget = self.create_finger_group_control(group_key, group_config)
                finger_control_layout.addWidget(group_widget)

            self.joint_control_layout.addWidget(finger_control_group)

        # 第二部分：创建独立关节控制组
        individual_joints = finger_groups['individual_joints']
        if individual_joints:
            individual_control_group = QGroupBox("独立关节控制")
            individual_control_layout = QVBoxLayout(individual_control_group)

            # 为每个独立关节创建控制组件
            for joint_name, joint_config in individual_joints.items():
                joint_widget = self.create_individual_joint_control(joint_name, joint_config)
                individual_control_layout.addWidget(joint_widget)

            self.joint_control_layout.addWidget(individual_control_group)

        # 第三部分：创建预设动作控制组
        preset_control_group = self.create_preset_control_group()
        self.joint_control_layout.addWidget(preset_control_group)

    def create_finger_group_control(self, group_key: str, group_config: Dict):
        """创建手指组控制"""
        # 根据控制类型设置不同的标题
        if 'thumb_rotation' in group_key:
            title = f"🔄 {group_config['name']}"
        elif 'thumb_bend' in group_key:
            title = f"✊ {group_config['name']}"
        else:
            title = f"👆 {group_config['name']}"

        frame = QGroupBox(title)
        layout = QVBoxLayout(frame)

        # 显示功能描述
        desc_label = QLabel(group_config['description'])
        desc_label.setStyleSheet("color: gray; font-size: 10px;")
        layout.addWidget(desc_label)

        # 显示包含的关节信息
        joints_text = " → ".join(group_config['joints'])
        info_label = QLabel(f"关节: {joints_text}")
        info_label.setStyleSheet("font-size: 10px;")
        layout.addWidget(info_label)

        # 计算联动控制的范围（使用第一个关节的范围）
        first_joint_info = list(group_config['joint_info'].values())[0]
        control_range = (first_joint_info['lower'], first_joint_info['upper'])

        # 创建滑块控制
        slider_layout = QHBoxLayout()
        slider = QSlider(Qt.Horizontal)
        slider.setMinimum(int(control_range[0] * 1000))  # 转换为整数
        slider.setMaximum(int(control_range[1] * 1000))
        slider.setValue(int((control_range[0] + control_range[1]) / 2 * 1000))

        # 数值显示
        value_label = QLabel(f"{(control_range[0] + control_range[1]) / 2:.3f}")

        def on_slider_change(value):
            actual_value = value / 1000.0
            value_label.setText(f"{actual_value:.3f}")
            # 只更新关节值，不立即更新视图
            self.update_joint_values_only(group_key, actual_value)

        def on_slider_released():
            # 鼠标松开时才更新视图
            actual_value = slider.value() / 1000.0
            self.pause_updates = False
            self.on_finger_group_change(group_key, actual_value)

        def on_slider_pressed():
            # 滑块按下时暂停其他更新
            self.pause_updates = True

        # 添加暂停更新功能
        if not hasattr(self, 'pause_updates'):
            self.pause_updates = False

        slider.valueChanged.connect(on_slider_change)
        slider.sliderReleased.connect(on_slider_released)
        slider.sliderPressed.connect(on_slider_pressed)

        slider_layout.addWidget(slider)
        slider_layout.addWidget(value_label)
        layout.addLayout(slider_layout)

        # 控制按钮
        btn_layout = QHBoxLayout()

        reset_btn = QPushButton("重置")
        reset_btn.clicked.connect(lambda: self.reset_finger_group(group_key, slider, value_label))
        btn_layout.addWidget(reset_btn)

        center_btn = QPushButton("居中")
        center_btn.clicked.connect(lambda: self.center_finger_group(group_key, slider, value_label))
        btn_layout.addWidget(center_btn)

        if 'thumb_rotation' in group_key:
            # 拇指转动控制按钮
            in_btn = QPushButton("内转")
            in_btn.clicked.connect(lambda: self.thumb_rotate_in(group_key, slider, value_label))
            btn_layout.addWidget(in_btn)

            out_btn = QPushButton("外转")
            out_btn.clicked.connect(lambda: self.thumb_rotate_out(group_key, slider, value_label))
            btn_layout.addWidget(out_btn)
        else:
            # 其他手指控制按钮
            close_btn = QPushButton("弯曲")
            close_btn.clicked.connect(lambda: self.close_finger_group(group_key, slider, value_label))
            btn_layout.addWidget(close_btn)

            open_btn = QPushButton("伸直")
            open_btn.clicked.connect(lambda: self.open_finger_group(group_key, slider, value_label))
            btn_layout.addWidget(open_btn)

        layout.addLayout(btn_layout)

        # 存储滑块引用
        self.finger_group_sliders[group_key] = slider

        return frame

    def create_individual_joint_control(self, joint_name: str, joint_config: Dict):
        """创建独立关节控制"""
        info = joint_config['info']
        frame = QGroupBox(joint_config['name'])
        layout = QVBoxLayout(frame)

        # 关节信息
        info_label = QLabel(f"类型: {info['type']} | 范围: [{info['lower']:.2f}, {info['upper']:.2f}]")
        info_label.setStyleSheet("font-size: 10px;")
        layout.addWidget(info_label)

        # 创建滑块
        slider_layout = QHBoxLayout()
        slider = QSlider(Qt.Horizontal)
        slider.setMinimum(int(info['lower'] * 1000))
        slider.setMaximum(int(info['upper'] * 1000))
        slider.setValue(int((info['lower'] + info['upper']) / 2 * 1000))

        # 数值显示
        value_label = QLabel(f"{(info['lower'] + info['upper']) / 2:.3f}")

        def on_slider_change(value):
            actual_value = value / 1000.0
            value_label.setText(f"{actual_value:.3f}")
            # 只更新关节值，不立即更新视图
            self.joint_values[joint_name] = actual_value

        def on_slider_released():
            # 鼠标松开时才更新视图
            actual_value = slider.value() / 1000.0
            self.on_individual_joint_change(joint_name, actual_value)
            self.pause_updates = False

        def on_slider_pressed():
            self.pause_updates = True

        slider.valueChanged.connect(on_slider_change)
        slider.sliderReleased.connect(on_slider_released)
        slider.sliderPressed.connect(on_slider_pressed)

        slider_layout.addWidget(slider)
        slider_layout.addWidget(value_label)
        layout.addLayout(slider_layout)

        # 控制按钮
        btn_layout = QHBoxLayout()

        reset_btn = QPushButton("重置")
        reset_btn.clicked.connect(lambda: self.reset_individual_joint(joint_name, slider, value_label))
        btn_layout.addWidget(reset_btn)

        center_btn = QPushButton("居中")
        center_btn.clicked.connect(lambda: self.center_individual_joint(joint_name, slider, value_label))
        btn_layout.addWidget(center_btn)

        layout.addLayout(btn_layout)

        # 存储滑块引用
        self.individual_joint_sliders[joint_name] = slider

        return frame

    def create_preset_control_group(self):
        """创建预设动作控制组"""
        preset_group = QGroupBox("预设动作")
        preset_layout = QVBoxLayout(preset_group)

        # 第一行按钮
        row1_layout = QHBoxLayout()

        reset_all_btn = QPushButton("全部重置")
        reset_all_btn.clicked.connect(self.reset_all_joints)
        row1_layout.addWidget(reset_all_btn)

        center_all_btn = QPushButton("全部居中")
        center_all_btn.clicked.connect(self.center_all_joints)
        row1_layout.addWidget(center_all_btn)

        random_btn = QPushButton("随机姿态")
        random_btn.clicked.connect(self.random_pose)
        row1_layout.addWidget(random_btn)

        preset_layout.addLayout(row1_layout)

        # 第二行按钮 - 预设动作
        row2_layout = QHBoxLayout()

        open_hand_btn = QPushButton("✋ 张开")
        open_hand_btn.clicked.connect(self.open_hand)
        row2_layout.addWidget(open_hand_btn)

        make_fist_btn = QPushButton("✊ 握拳")
        make_fist_btn.clicked.connect(self.make_fist)
        row2_layout.addWidget(make_fist_btn)

        pinch_btn = QPushButton("🤏 捏取")
        pinch_btn.clicked.connect(self.pinch_pose)
        row2_layout.addWidget(pinch_btn)

        preset_layout.addLayout(row2_layout)

        return preset_group

    def update_joint_values_only(self, group_key: str, control_value: float):
        """只更新关节值，不触发视图更新（用于滑动过程中）"""
        if not self.finger_group_analyzer:
            return

        try:
            finger_groups = self.finger_group_analyzer.get_finger_groups()
            group_config = finger_groups['finger_groups'][group_key]

            # 根据联动比例设置各个关节值
            for i, joint_name in enumerate(group_config['joints']):
                ratio = group_config['ratios'][i]
                joint_info = group_config['joint_info'][joint_name]

                # 计算实际关节角度
                joint_value = control_value * ratio

                # 限制在关节范围内
                joint_value = max(joint_info['lower'], min(joint_info['upper'], joint_value))

                # 只更新关节值，不触发视图更新
                self.joint_values[joint_name] = joint_value

        except (ValueError, KeyError) as e:
            print(f"更新关节值错误: {e}")

    def on_finger_group_change(self, group_key: str, control_value: float):
        print(group_key, control_value, 888888888)
        """
        手指联动控制回调函数

        当用户操作手指分组滑块时调用，实现多个关节的协调运动。
        根据预定义的联动比例，将单个控制值转换为多个关节的目标值。

        Args:
            group_key (str): 手指分组标识符
            control_value (float): 用户输入的控制值
        """
        # 检查必要组件是否已初始化
        if not self.finger_group_analyzer or not self.transform_calculator:
            return

        # 如果正在拖拽滑块，跳过更新以提高性能
        if getattr(self, 'pause_updates', False):
            return

        try:
            finger_groups = self.finger_group_analyzer.get_finger_groups()
            group_config = finger_groups['finger_groups'][group_key]

            affected_links = set()  # 收集所有受影响的链接

            # 根据联动比例计算每个关节的目标值
            for i, joint_name in enumerate(group_config['joints']):
                ratio = group_config['ratios'][i]  # 获取该关节的联动比例
                joint_info = group_config['joint_info'][joint_name]

                # 应用联动比例计算实际关节角度
                joint_value = control_value * ratio

                # 限制关节值在有效范围内，防止超出物理限制
                joint_value = max(joint_info['lower'], min(joint_info['upper'], joint_value))

                # 更新关节值并获取受影响的链接
                links = self.transform_calculator.update_joint_value(joint_name, joint_value)
                affected_links.update(links)
                self.joint_values[joint_name] = joint_value

            # 批量更新受影响的网格，提高渲染效率
            if affected_links:
                self._update_affected_meshes(affected_links)

        except (ValueError, KeyError) as e:
            print(f"手指联动控制错误: {e}")

    def on_individual_joint_change(self, joint_name: str, value: float):
        """独立关节控制回调"""
        if not self.transform_calculator:
            return

        # 如果正在拖拽滑块，跳过更新
        if getattr(self, 'pause_updates', False):
            return

        try:
            # 更新关节值并获取受影响的链接
            affected_links = self.transform_calculator.update_joint_value(joint_name, value)
            self.joint_values[joint_name] = value

            # 更新受影响的网格
            if affected_links:
                self._update_affected_meshes(affected_links)

        except Exception as e:
            print(f"独立关节控制错误: {e}")

    def _update_affected_meshes(self, affected_links):
        """
        更新受影响的网格显示

        当关节运动时，只更新受影响的链接网格，而不是重新渲染整个模型。
        这种选择性更新策略大大提高了实时交互的性能。
        使用顶点缓存进一步优化重复计算。

        Args:
            affected_links (set): 受影响的链接名称集合
        """
        if not affected_links:
            return

        try:
            # 遍历所有网格项，只更新受影响的链接
            for mesh_item in self.mesh_items:
                if hasattr(mesh_item, 'link_name') and mesh_item.link_name in affected_links:
                    link_name = mesh_item.link_name
                    if link_name in self.mesh_data:
                        # 获取原始网格数据（未变换的）
                        vertices = self.mesh_data[link_name]['vertices']
                        faces = self.mesh_data[link_name]['faces']

                        # 获取链接的当前变换矩阵
                        link_transform = self.transform_calculator.get_link_transform(link_name)

                        # 应用视觉变换（如果存在）
                        if link_name in self.visual_transforms:
                            transform = np.dot(link_transform, self.visual_transforms[link_name])
                        else:
                            transform = link_transform

                        # 创建缓存键，用于避免重复计算
                        cache_key = (link_name, id(transform))

                        # 检查缓存中是否已有变换后的顶点
                        if cache_key in self.vertex_cache:
                            vertices_transformed = self.vertex_cache[cache_key]
                        else:
                            # 计算变换后的顶点
                            vertices_homogeneous = np.hstack((vertices, np.ones((vertices.shape[0], 1))))
                            vertices_transformed = np.dot(vertices_homogeneous, transform.T)[:, :3]

                            # 限制缓存大小，防止内存过度使用
                            if len(self.vertex_cache) > 50:
                                self.vertex_cache.clear()

                            # 缓存变换结果
                            self.vertex_cache[cache_key] = vertices_transformed

                        # 更新网格显示数据
                        mesh_item.setMeshData(vertexes=vertices_transformed, faces=faces)

            # 触发异步视图更新，避免阻塞UI
            if not self.pending_update:
                self.pending_update = True
                self.update_timer.start()

        except Exception as e:
            print(f"更新网格错误: {e}")



    def _update_view(self):
        """
        更新3D视图显示

        这是一个轻量级的视图更新函数，用于刷新3D渲染。
        包含视图更新阻塞检查，防止在不合适的时机进行更新。
        通过定时器调用，实现异步更新以保持UI响应性。
        """
        # 检查是否允许更新视图
        if self.view_update_blocked:
            return

        # 清除待更新标志
        self.pending_update = False

        # 刷新3D视图
        self.view_widget.update()

    # 手指组控制方法
    def reset_finger_group(self, group_key: str, slider: QSlider, value_label: QLabel):
        """重置手指组"""
        if not self.finger_group_analyzer:
            return
        finger_groups = self.finger_group_analyzer.get_finger_groups()
        group_config = finger_groups['finger_groups'][group_key]
        first_joint_info = list(group_config['joint_info'].values())[0]
        initial_pos = first_joint_info['lower']
        slider.setValue(int(initial_pos * 1000))
        value_label.setText(f"{initial_pos:.3f}")
        self.on_finger_group_change(group_key, initial_pos)

    def center_finger_group(self, group_key: str, slider: QSlider, value_label: QLabel):
        """居中手指组"""
        if not self.finger_group_analyzer:
            return
        finger_groups = self.finger_group_analyzer.get_finger_groups()
        group_config = finger_groups['finger_groups'][group_key]
        first_joint_info = list(group_config['joint_info'].values())[0]
        center_pos = (first_joint_info['lower'] + first_joint_info['upper']) / 2
        slider.setValue(int(center_pos * 1000))
        value_label.setText(f"{center_pos:.3f}")
        self.on_finger_group_change(group_key, center_pos)

    def close_finger_group(self, group_key: str, slider: QSlider, value_label: QLabel):
        """握紧手指组"""
        if not self.finger_group_analyzer:
            return
        finger_groups = self.finger_group_analyzer.get_finger_groups()
        group_config = finger_groups['finger_groups'][group_key]
        first_joint_info = list(group_config['joint_info'].values())[0]
        close_pos = first_joint_info['upper']
        slider.setValue(int(close_pos * 1000))
        value_label.setText(f"{close_pos:.3f}")
        self.on_finger_group_change(group_key, close_pos)

    def open_finger_group(self, group_key: str, slider: QSlider, value_label: QLabel):
        """张开手指组"""
        if not self.finger_group_analyzer:
            return
        finger_groups = self.finger_group_analyzer.get_finger_groups()
        group_config = finger_groups['finger_groups'][group_key]
        first_joint_info = list(group_config['joint_info'].values())[0]
        open_pos = first_joint_info['lower']
        slider.setValue(int(open_pos * 1000))
        value_label.setText(f"{open_pos:.3f}")
        self.on_finger_group_change(group_key, open_pos)

    def thumb_rotate_in(self, group_key: str, slider: QSlider, value_label: QLabel):
        """拇指内转"""
        if 'thumb_rotation' in group_key and self.finger_group_analyzer:
            finger_groups = self.finger_group_analyzer.get_finger_groups()
            group_config = finger_groups['finger_groups'][group_key]
            first_joint_info = list(group_config['joint_info'].values())[0]
            rotate_pos = first_joint_info['upper']
            slider.setValue(int(rotate_pos * 1000))
            value_label.setText(f"{rotate_pos:.3f}")
            self.on_finger_group_change(group_key, rotate_pos)

    def thumb_rotate_out(self, group_key: str, slider: QSlider, value_label: QLabel):
        """拇指外转"""
        if 'thumb_rotation' in group_key and self.finger_group_analyzer:
            finger_groups = self.finger_group_analyzer.get_finger_groups()
            group_config = finger_groups['finger_groups'][group_key]
            first_joint_info = list(group_config['joint_info'].values())[0]
            rotate_pos = first_joint_info['lower']
            slider.setValue(int(rotate_pos * 1000))
            value_label.setText(f"{rotate_pos:.3f}")
            self.on_finger_group_change(group_key, rotate_pos)

    # 独立关节控制方法
    def reset_individual_joint(self, joint_name: str, slider: QSlider, value_label: QLabel):
        """重置独立关节"""
        if not self.finger_group_analyzer:
            return
        finger_groups = self.finger_group_analyzer.get_finger_groups()
        joint_config = finger_groups['individual_joints'][joint_name]
        info = joint_config['info']
        initial_pos = info['lower']
        slider.setValue(int(initial_pos * 1000))
        value_label.setText(f"{initial_pos:.3f}")
        self.on_individual_joint_change(joint_name, initial_pos)

    def center_individual_joint(self, joint_name: str, slider: QSlider, value_label: QLabel):
        """居中独立关节"""
        if not self.finger_group_analyzer:
            return
        finger_groups = self.finger_group_analyzer.get_finger_groups()
        joint_config = finger_groups['individual_joints'][joint_name]
        info = joint_config['info']
        center_pos = (info['lower'] + info['upper']) / 2
        slider.setValue(int(center_pos * 1000))
        value_label.setText(f"{center_pos:.3f}")
        self.on_individual_joint_change(joint_name, center_pos)

    # 全局控制方法
    def reset_all_joints(self):
        """重置所有关节"""
        if not self.finger_group_analyzer:
            return
        finger_groups = self.finger_group_analyzer.get_finger_groups()

        # 重置手指组
        for group_key in finger_groups['finger_groups']:
            if group_key in self.finger_group_sliders:
                slider = self.finger_group_sliders[group_key]
                # 找到对应的value_label（这里简化处理）
                group_config = finger_groups['finger_groups'][group_key]
                first_joint_info = list(group_config['joint_info'].values())[0]
                initial_pos = first_joint_info['lower']
                slider.setValue(int(initial_pos * 1000))
                self.on_finger_group_change(group_key, initial_pos)

        # 重置独立关节
        for joint_name in finger_groups['individual_joints']:
            if joint_name in self.individual_joint_sliders:
                slider = self.individual_joint_sliders[joint_name]
                joint_config = finger_groups['individual_joints'][joint_name]
                info = joint_config['info']
                initial_pos = info['lower']
                slider.setValue(int(initial_pos * 1000))
                self.on_individual_joint_change(joint_name, initial_pos)

    def center_all_joints(self):
        """将所有关节设置到中间位置"""
        if not self.finger_group_analyzer:
            return
        finger_groups = self.finger_group_analyzer.get_finger_groups()

        # 居中手指组
        for group_key in finger_groups['finger_groups']:
            if group_key in self.finger_group_sliders:
                slider = self.finger_group_sliders[group_key]
                group_config = finger_groups['finger_groups'][group_key]
                first_joint_info = list(group_config['joint_info'].values())[0]
                center_pos = (first_joint_info['lower'] + first_joint_info['upper']) / 2
                slider.setValue(int(center_pos * 1000))
                self.on_finger_group_change(group_key, center_pos)

        # 居中独立关节
        for joint_name in finger_groups['individual_joints']:
            if joint_name in self.individual_joint_sliders:
                slider = self.individual_joint_sliders[joint_name]
                joint_config = finger_groups['individual_joints'][joint_name]
                info = joint_config['info']
                center_pos = (info['lower'] + info['upper']) / 2
                slider.setValue(int(center_pos * 1000))
                self.on_individual_joint_change(joint_name, center_pos)

    def random_pose(self):
        """设置随机姿态"""
        if not self.finger_group_analyzer:
            return
        finger_groups = self.finger_group_analyzer.get_finger_groups()

        # 随机设置手指组
        for group_key, group_config in finger_groups['finger_groups'].items():
            if group_key in self.finger_group_sliders:
                slider = self.finger_group_sliders[group_key]
                first_joint_info = list(group_config['joint_info'].values())[0]
                random_pos = np.random.uniform(first_joint_info['lower'], first_joint_info['upper'])
                slider.setValue(int(random_pos * 1000))
                self.on_finger_group_change(group_key, random_pos)

        # 随机设置独立关节
        for joint_name, joint_config in finger_groups['individual_joints'].items():
            if joint_name in self.individual_joint_sliders:
                slider = self.individual_joint_sliders[joint_name]
                info = joint_config['info']
                random_pos = np.random.uniform(info['lower'], info['upper'])
                slider.setValue(int(random_pos * 1000))
                self.on_individual_joint_change(joint_name, random_pos)

    def make_fist(self):
        """
        执行握拳动作预设

        将所有手指关节设置到最大弯曲位置，模拟握拳动作。
        这是一个常用的机器人手部测试姿态，用于验证所有关节的运动能力。
        """
        if not self.finger_group_analyzer:
            return
        finger_groups = self.finger_group_analyzer.get_finger_groups()

        # 设置所有手指组到最大弯曲位置
        for group_key in finger_groups['finger_groups']:
            if group_key in self.finger_group_sliders:
                slider = self.finger_group_sliders[group_key]
                group_config = finger_groups['finger_groups'][group_key]
                first_joint_info = list(group_config['joint_info'].values())[0]

                if 'thumb_rotation' in group_key:
                    # 拇指内转，准备对掌运动
                    fist_pos = first_joint_info['upper']
                else:
                    # 其他手指弯曲到最大角度
                    fist_pos = first_joint_info['upper']

                # 更新滑块位置并触发关节运动
                slider.setValue(int(fist_pos * 1000))
                self.on_finger_group_change(group_key, fist_pos)

        # 设置所有独立关节到最大位置
        for joint_name, joint_config in finger_groups['individual_joints'].items():
            if joint_name in self.individual_joint_sliders:
                slider = self.individual_joint_sliders[joint_name]
                info = joint_config['info']
                fist_pos = info['upper']  # 使用关节的上限值
                slider.setValue(int(fist_pos * 1000))
                self.on_individual_joint_change(joint_name, fist_pos)

    def open_hand(self):
        """
        执行张开手掌动作预设

        将所有手指关节设置到最小角度位置，模拟手掌完全张开的状态。
        这是机器人手部的默认休息姿态，也是抓取动作的起始位置。
        """
        if not self.finger_group_analyzer:
            return
        finger_groups = self.finger_group_analyzer.get_finger_groups()

        # 设置所有手指组到最小角度位置（伸直状态）
        for group_key in finger_groups['finger_groups']:
            if group_key in self.finger_group_sliders:
                slider = self.finger_group_sliders[group_key]
                group_config = finger_groups['finger_groups'][group_key]
                first_joint_info = list(group_config['joint_info'].values())[0]

                if 'thumb_rotation' in group_key:
                    # 拇指外转，远离手心，为抓取做准备
                    open_pos = first_joint_info['lower']
                else:
                    # 其他手指完全伸直
                    open_pos = first_joint_info['lower']

                # 更新滑块位置并触发关节运动
                slider.setValue(int(open_pos * 1000))
                self.on_finger_group_change(group_key, open_pos)

        # 设置所有独立关节到最小位置
        for joint_name, joint_config in finger_groups['individual_joints'].items():
            if joint_name in self.individual_joint_sliders:
                slider = self.individual_joint_sliders[joint_name]
                info = joint_config['info']
                open_pos = info['lower']  # 使用关节的下限值
                slider.setValue(int(open_pos * 1000))
                self.on_individual_joint_change(joint_name, open_pos)

    def pinch_pose(self):
        """捏取姿态"""
        if not self.finger_group_analyzer:
            return
        finger_groups = self.finger_group_analyzer.get_finger_groups()

        for group_key in finger_groups['finger_groups']:
            if group_key in self.finger_group_sliders:
                slider = self.finger_group_sliders[group_key]
                group_config = finger_groups['finger_groups'][group_key]
                first_joint_info = list(group_config['joint_info'].values())[0]

                if 'thumb_rotation' in group_key:
                    # 拇指内转到中间位置
                    pinch_pos = (first_joint_info['lower'] + first_joint_info['upper']) / 2
                elif 'thumb_bend' in group_key:
                    # 拇指适度弯曲
                    pinch_pos = (first_joint_info['lower'] + first_joint_info['upper']) * 0.7
                elif 'index' in group_key:
                    # 食指适度弯曲
                    pinch_pos = (first_joint_info['lower'] + first_joint_info['upper']) * 0.6
                else:
                    # 其他手指稍微弯曲
                    pinch_pos = (first_joint_info['lower'] + first_joint_info['upper']) * 0.3

                slider.setValue(int(pinch_pos * 1000))
                self.on_finger_group_change(group_key, pinch_pos)

    # 相机和材质控制方法
    def update_camera_distance(self, value):
        """
        更新相机距离

        调整3D视图中相机到模型的距离，实现缩放效果。
        同时保存相机状态以便后续恢复。

        Args:
            value (int): 滑块值 (1-1000，对应实际距离0.1-100)
        """
        # 如果视图更新被阻塞，跳过操作
        if self.view_update_blocked:
            return

        # 将滑块值转换为实际距离值 (1-1000 -> 0.1-100)
        actual_distance = value / 100.0

        self.camera_distance = actual_distance
        self.camera_state['distance'] = actual_distance

        # 更新标签显示
        self.distance_value_label.setText(f"{actual_distance:.1f}")

        # 保存当前相机状态，确保其他参数不变
        current_state = self.view_widget.cameraParams()
        self.camera_state['elevation'] = current_state.get('elevation', 30.0)
        self.camera_state['azimuth'] = current_state.get('azimuth', 45.0)

        # 只更新距离参数，保持仰角和方位角不变
        self.view_widget.setCameraPosition(distance=actual_distance)

    def update_camera_azimuth(self, value):
        """
        更新相机方位角

        调整3D视图中相机的方位角，实现绕模型水平旋转的效果。

        Args:
            value (int): 新的方位角度值（0-360度）
        """
        # 如果视图更新被阻塞，跳过操作
        if self.view_update_blocked:
            return

        self.camera_state['azimuth'] = value

        # 更新标签显示
        self.azimuth_value_label.setText(f"{value}°")

        # 保存当前距离和仰角，确保其他参数不变
        current_distance = self.camera_state['distance']
        current_elevation = self.camera_state['elevation']

        # 更新方位角，实现水平旋转
        self.view_widget.setCameraParams(elevation=current_elevation, azimuth=value)
        self.view_widget.setCameraPosition(distance=current_distance)

    def update_camera_elevation(self, value):
        """
        更新相机仰角

        调整3D视图中相机的仰角，实现垂直视角变化的效果。

        Args:
            value (int): 新的仰角度值（-90到90度）
        """
        # 如果视图更新被阻塞，跳过操作
        if self.view_update_blocked:
            return

        self.camera_state['elevation'] = value

        # 更新标签显示
        self.elevation_value_label.setText(f"{value}°")

        # 保存当前距离和方位角，确保其他参数不变
        current_distance = self.camera_state['distance']
        current_azimuth = self.camera_state['azimuth']

        # 更新仰角，实现垂直旋转
        self.view_widget.setCameraParams(elevation=value, azimuth=current_azimuth)
        self.view_widget.setCameraPosition(distance=current_distance)

    def update_camera_rotation(self, value):
        """
        更新相机旋转角度（保持向后兼容）

        Args:
            value (int): 新的方位角度值（0-360度）
        """
        self.update_camera_azimuth(value)

    def update_camera_center_x(self, value):
        """
        更新相机观察中心的X坐标

        Args:
            value (int): 新的X坐标值（-100到100，对应-10.0到10.0）
        """
        if self.view_update_blocked:
            return

        x_value = value / 10.0  # 转换为实际坐标值
        self.camera_state['center'][0] = x_value

        # 更新标签显示
        self.center_x_value_label.setText(f"{x_value:.1f}")

        # 更新相机中心
        center = self.camera_state['center']
        self.view_widget.setCameraPosition(
            pos=QVector3D(center[0], center[1], center[2]),
            distance=self.camera_state['distance']
        )

    def update_camera_center_y(self, value):
        """
        更新相机观察中心的Y坐标

        Args:
            value (int): 新的Y坐标值（-100到100，对应-10.0到10.0）
        """
        if self.view_update_blocked:
            return

        y_value = value / 10.0  # 转换为实际坐标值
        self.camera_state['center'][1] = y_value

        # 更新标签显示
        self.center_y_value_label.setText(f"{y_value:.1f}")

        # 更新相机中心
        center = self.camera_state['center']
        self.view_widget.setCameraPosition(
            pos=QVector3D(center[0], center[1], center[2]),
            distance=self.camera_state['distance']
        )

    def update_camera_center_z(self, value):
        """
        更新相机观察中心的Z坐标

        Args:
            value (int): 新的Z坐标值（-100到100，对应-10.0到10.0）
        """
        if self.view_update_blocked:
            return

        z_value = value / 10.0  # 转换为实际坐标值
        self.camera_state['center'][2] = z_value

        # 更新标签显示
        self.center_z_value_label.setText(f"{z_value:.1f}")

        # 更新相机中心
        center = self.camera_state['center']
        self.view_widget.setCameraPosition(
            pos=QVector3D(center[0], center[1], center[2]),
            distance=self.camera_state['distance']
        )

    def reset_camera_center(self):
        """重置相机观察中心到原点"""
        if self.view_update_blocked:
            return

        self.view_update_blocked = True

        # 重置中心坐标
        self.camera_state['center'] = [0, 0, 0]

        # 更新滑块但不触发事件
        self.center_x_slider.blockSignals(True)
        self.center_y_slider.blockSignals(True)
        self.center_z_slider.blockSignals(True)

        self.center_x_slider.setValue(0)
        self.center_y_slider.setValue(0)
        self.center_z_slider.setValue(0)

        self.center_x_slider.blockSignals(False)
        self.center_y_slider.blockSignals(False)
        self.center_z_slider.blockSignals(False)

        # 更新标签
        self.center_x_value_label.setText("0.0")
        self.center_y_value_label.setText("0.0")
        self.center_z_value_label.setText("0.0")

        # 更新相机
        self.view_widget.setCameraPosition(
            pos=QVector3D(0, 0, 0),
            distance=self.camera_state['distance']
        )

        self.view_update_blocked = False

    def auto_center_model(self):
        """自动计算并设置模型的中心位置"""
        if not self.robot or not self.mesh_items:
            return

        try:
            # 计算所有网格的边界框
            all_vertices = []
            for mesh_item in self.mesh_items:
                if hasattr(mesh_item, 'meshdata') and mesh_item.meshdata is not None:
                    vertices = mesh_item.meshdata.vertexes()
                    if vertices is not None and len(vertices) > 0:
                        all_vertices.extend(vertices)

            if not all_vertices:
                return

            # 转换为numpy数组
            all_vertices = np.array(all_vertices)

            # 计算边界框中心
            min_coords = np.min(all_vertices, axis=0)
            max_coords = np.max(all_vertices, axis=0)
            center = (min_coords + max_coords) / 2

            # 设置相机中心
            self.view_update_blocked = True

            self.camera_state['center'] = center.tolist()

            # 更新滑块
            self.center_x_slider.blockSignals(True)
            self.center_y_slider.blockSignals(True)
            self.center_z_slider.blockSignals(True)

            self.center_x_slider.setValue(int(center[0] * 10))
            self.center_y_slider.setValue(int(center[1] * 10))
            self.center_z_slider.setValue(int(center[2] * 10))

            self.center_x_slider.blockSignals(False)
            self.center_y_slider.blockSignals(False)
            self.center_z_slider.blockSignals(False)

            # 更新标签
            self.center_x_value_label.setText(f"{center[0]:.1f}")
            self.center_y_value_label.setText(f"{center[1]:.1f}")
            self.center_z_value_label.setText(f"{center[2]:.1f}")

            # 更新相机
            self.view_widget.setCameraPosition(
                pos=QVector3D(center[0], center[1], center[2]),
                distance=self.camera_state['distance']
            )

            self.view_update_blocked = False

        except Exception as e:
            print(f"自动居中模型时出错: {e}")
            self.view_update_blocked = False

    def set_camera_parameters(self, distance=None, azimuth=None, elevation=None,
                            center_x=None, center_y=None, center_z=None,
                            update_ui=True, animate=False):
        """
        一次性设置所有相机参数并生效

        Args:
            distance (float, optional): 相机距离 (1-50)
            azimuth (float, optional): 方位角度 (0-360)
            elevation (float, optional): 仰角度 (-90到90)
            center_x (float, optional): X轴中心位置 (-10.0到10.0)
            center_y (float, optional): Y轴中心位置 (-10.0到10.0)
            center_z (float, optional): Z轴中心位置 (-10.0到10.0)
            update_ui (bool): 是否同时更新UI控件，默认True
            animate (bool): 是否使用动画过渡，默认False

        Returns:
            dict: 设置后的实际参数值

        Example:
            # 设置为正面视角，距离15，观察中心在(1, 0, 2)
            viewer.set_camera_parameters(
                distance=15,
                azimuth=0,
                elevation=0,
                center_x=1.0,
                center_y=0.0,
                center_z=2.0
            )

            # 只设置距离和角度，保持中心位置不变
            viewer.set_camera_parameters(distance=20, azimuth=90)
        """
        if self.view_update_blocked:
            return None

        self.view_update_blocked = True

        try:
            # 记录原始值用于动画或回退
            original_values = {
                'distance': self.camera_state['distance'],
                'azimuth': self.camera_state['azimuth'],
                'elevation': self.camera_state['elevation'],
                'center': self.camera_state['center'].copy()
            }

            # 更新相机状态（只更新提供的参数）
            if distance is not None:
                distance = max(0.1, min(100.0, distance))  # 限制范围0.1-100
                self.camera_state['distance'] = distance

            if azimuth is not None:
                azimuth = azimuth % 360  # 确保在0-360范围内
                self.camera_state['azimuth'] = azimuth

            if elevation is not None:
                elevation = max(-90, min(90, elevation))  # 限制范围
                self.camera_state['elevation'] = elevation

            if center_x is not None:
                center_x = max(-10.0, min(10.0, center_x))  # 限制范围
                self.camera_state['center'][0] = center_x

            if center_y is not None:
                center_y = max(-10.0, min(10.0, center_y))  # 限制范围
                self.camera_state['center'][1] = center_y

            if center_z is not None:
                center_z = max(-10.0, min(10.0, center_z))  # 限制范围
                self.camera_state['center'][2] = center_z

            # 更新UI控件（如果需要）
            if update_ui:
                self._update_camera_ui_controls()

            # 应用相机设置
            self._apply_camera_settings()

            # 返回实际设置的值
            result = {
                'distance': self.camera_state['distance'],
                'azimuth': self.camera_state['azimuth'],
                'elevation': self.camera_state['elevation'],
                'center_x': self.camera_state['center'][0],
                'center_y': self.camera_state['center'][1],
                'center_z': self.camera_state['center'][2]
            }

            print(f"相机参数已设置: {result}")
            return result

        except Exception as e:
            print(f"设置相机参数时出错: {e}")
            return None

        finally:
            self.view_update_blocked = False

    def _update_camera_ui_controls(self):
        """更新UI控件以反映当前相机状态"""
        # 阻止信号触发以避免递归调用

        # 距离滑块 (实际距离转换为滑块值: 距离*10)
        self.distance_slider.blockSignals(True)
        self.distance_slider.setValue(int(self.camera_state['distance'] * 10))
        self.distance_slider.blockSignals(False)
        self.distance_value_label.setText(f"{self.camera_state['distance']:.1f}")

        # 方位角滑块
        self.azimuth_slider.blockSignals(True)
        self.azimuth_slider.setValue(int(self.camera_state['azimuth']))
        self.azimuth_slider.blockSignals(False)
        self.azimuth_value_label.setText(f"{int(self.camera_state['azimuth'])}°")

        # 仰角滑块
        self.elevation_slider.blockSignals(True)
        self.elevation_slider.setValue(int(self.camera_state['elevation']))
        self.elevation_slider.blockSignals(False)
        self.elevation_value_label.setText(f"{int(self.camera_state['elevation'])}°")

        # 中心位置滑块 (实际坐标转换为滑块值: 坐标*10)
        center_controls = [
            (self.center_x_slider, self.center_x_value_label, self.camera_state['center'][0]),
            (self.center_y_slider, self.center_y_value_label, self.camera_state['center'][1]),
            (self.center_z_slider, self.center_z_value_label, self.camera_state['center'][2])
        ]

        for slider, label, value in center_controls:
            slider.blockSignals(True)
            slider.setValue(int(value))
            slider.blockSignals(False)
            label.setText(f"{value:.1f}")

    def _apply_camera_settings(self):
        """应用当前相机设置到3D视图"""
        print(self.camera_state)
        try:

            # 设置相机位置
            center = self.camera_state['center']
            self.view_widget.setCameraPosition(
                distance=self.camera_state['distance'],
                pos=QVector3D(center[0], center[1], center[2])
            )
            # 设置相机参数
            self.view_widget.setCameraParams(
                elevation=self.camera_state['elevation'],
                azimuth=self.camera_state['azimuth']
            )

        except Exception as e:
            print(f"应用相机设置时出错: {e}")

    def set_camera_preset(self, preset_name):
        """
        设置相机预设视角

        Args:
            preset_name (str): 预设名称 ("front", "side", "top", "isometric", "bottom", "back")
        """
        if self.view_update_blocked:
            return

        self.view_update_blocked = True

        # 定义预设参数
        presets = {
            "front": {"elevation": 0, "azimuth": 0, "distance": 15},
            "side": {"elevation": 0, "azimuth": 90, "distance": 15},
            "top": {"elevation": 90, "azimuth": 0, "distance": 15},
            "bottom": {"elevation": -90, "azimuth": 0, "distance": 15},
            "back": {"elevation": 0, "azimuth": 180, "distance": 15},
            "isometric": {"elevation": 30, "azimuth": 45, "distance": 15}
        }

        if preset_name not in presets:
            self.view_update_blocked = False
            return

        preset = presets[preset_name]

        # 更新相机状态
        self.camera_state['elevation'] = preset['elevation']
        self.camera_state['azimuth'] = preset['azimuth']
        self.camera_state['distance'] = preset['distance']

        # 更新滑块但不触发事件
        self.distance_slider.blockSignals(True)
        self.azimuth_slider.blockSignals(True)
        self.elevation_slider.blockSignals(True)

        self.distance_slider.setValue(preset['distance'])
        self.azimuth_slider.setValue(preset['azimuth'])
        self.elevation_slider.setValue(preset['elevation'])

        self.distance_slider.blockSignals(False)
        self.azimuth_slider.blockSignals(False)
        self.elevation_slider.blockSignals(False)

        # 更新标签
        self.distance_value_label.setText(str(preset['distance']))
        self.azimuth_value_label.setText(f"{preset['azimuth']}°")
        self.elevation_value_label.setText(f"{preset['elevation']}°")

        # 应用相机设置
        self.view_widget.setCameraParams(
            elevation=preset['elevation'],
            azimuth=preset['azimuth']
        )
        center = self.camera_state['center']
        self.view_widget.setCameraPosition(
            distance=preset['distance'],
            pos=QVector3D(center[0], center[1], center[2])
        )

        self.view_update_blocked = False

    def reset_camera(self):
        """重置相机到默认状态"""
        self.view_update_blocked = True  # 阻止更新冲突

        # 重置相机状态
        self.camera_distance = 10.0
        self.camera_rotation = 0
        self.camera_state = {
            'distance': 10.0,
            'elevation': 30.0,
            'azimuth': 45.0,
            'center': [0, 0, 0]
        }

        # 更新所有滑块但不触发事件
        self.distance_slider.blockSignals(True)
        self.azimuth_slider.blockSignals(True)
        self.elevation_slider.blockSignals(True)
        self.center_x_slider.blockSignals(True)
        self.center_y_slider.blockSignals(True)
        self.center_z_slider.blockSignals(True)

        self.distance_slider.setValue(100)  # 滑块值100对应距离10.0
        self.azimuth_slider.setValue(45)
        self.elevation_slider.setValue(30)
        self.center_x_slider.setValue(0)
        self.center_y_slider.setValue(0)
        self.center_z_slider.setValue(0)

        self.distance_slider.blockSignals(False)
        self.azimuth_slider.blockSignals(False)
        self.elevation_slider.blockSignals(False)
        self.center_x_slider.blockSignals(False)
        self.center_y_slider.blockSignals(False)
        self.center_z_slider.blockSignals(False)

        # 更新标签
        self.distance_value_label.setText("10.0")
        self.azimuth_value_label.setText("45°")
        self.elevation_value_label.setText("30°")
        self.center_x_value_label.setText("0.0")
        self.center_y_value_label.setText("0.0")
        self.center_z_value_label.setText("0.0")

        # 重置视图
        self.view_widget.setCameraPosition(distance=10.0, pos=QVector3D(0, 0, 0))
        self.view_widget.setCameraParams(elevation=30.0, azimuth=45.0)

        self.view_update_blocked = False

    def save_camera_state(self):
        """保存当前相机状态"""
        try:
            current_params = self.view_widget.cameraParams()
            if current_params:
                self.camera_state.update(current_params)
        except:
            pass  # 如果获取失败，使用默认状态

    def restore_camera_state(self):
        """恢复相机状态"""
        try:
            self.view_widget.setCameraParams(**self.camera_state)
        except:
            # 如果恢复失败，使用默认设置
            self.view_widget.setCameraPosition(distance=self.camera_state['distance'])
            self.view_widget.setCameraParams(
                elevation=self.camera_state['elevation'],
                azimuth=self.camera_state['azimuth']
            )

    def apply_link_material(self, mesh_item, link_name):
        """为链接应用材质"""
        if link_name in self.link_materials:
            # 使用URDF中定义的颜色
            color = self.link_materials[link_name]['color']
            mesh_item.setColor(tuple(color))
        else:
            # 使用默认颜色，但根据链接类型设置不同颜色
            if 'base' in link_name.lower():
                mesh_item.setColor((0.8, 0.8, 0.9, 1.0))  # 浅蓝灰色
            elif 'thumb' in link_name.lower():
                mesh_item.setColor((0.9, 0.7, 0.7, 1.0))  # 浅红色
            elif 'index' in link_name.lower():
                mesh_item.setColor((0.7, 0.9, 0.7, 1.0))  # 浅绿色
            elif 'middle' in link_name.lower():
                mesh_item.setColor((0.7, 0.7, 0.9, 1.0))  # 浅蓝色
            elif 'ring' in link_name.lower():
                mesh_item.setColor((0.9, 0.9, 0.7, 1.0))  # 浅黄色
            elif 'pinky' in link_name.lower():
                mesh_item.setColor((0.9, 0.7, 0.9, 1.0))  # 浅紫色
            else:
                mesh_item.setColor((0.8, 0.8, 0.8, 1.0))  # 默认灰色

    def update_material_type(self, material_type):
        """更新材质类型"""
        self.current_material_type = material_type
        self.update_material_properties()

    def update_material_properties(self):
        """更新材质属性"""
        glossiness = self.glossiness_slider.value() / 100.0
        metalness = self.metalness_slider.value() / 100.0
        transparency = self.transparency_slider.value() / 100.0

        for item in self.mesh_items:
            if hasattr(item, 'link_name'):
                link_name = item.link_name

                # 获取基础颜色
                if self.current_material_type == "Default":
                    self.apply_link_material(item, link_name)
                else:
                    # 根据材质类型调整颜色
                    base_color = self.get_base_color(link_name)
                    final_color = self.apply_material_effect(base_color, glossiness, metalness, transparency)
                    item.setColor(final_color)

                # 设置渲染选项
                if transparency > 0:
                    item.setGLOptions('translucent')
                else:
                    item.setGLOptions('opaque')

                # 材质类型特定的渲染设置已通过颜色和透明度体现

        # 使用定时器延迟更新，避免频繁刷新
        if not self.pending_update and not self.view_update_blocked:
            self.pending_update = True
            self.update_timer.start()

    def get_base_color(self, link_name):
        """获取链接的基础颜色"""
        if link_name in self.link_materials:
            return self.link_materials[link_name]['color']
        elif 'base' in link_name.lower():
            return [0.8, 0.8, 0.9, 1.0]
        elif 'thumb' in link_name.lower():
            return [0.9, 0.7, 0.7, 1.0]
        elif 'index' in link_name.lower():
            return [0.7, 0.9, 0.7, 1.0]
        elif 'middle' in link_name.lower():
            return [0.7, 0.7, 0.9, 1.0]
        elif 'ring' in link_name.lower():
            return [0.9, 0.9, 0.7, 1.0]
        elif 'pinky' in link_name.lower():
            return [0.9, 0.7, 0.9, 1.0]
        else:
            return [0.8, 0.8, 0.8, 1.0]

    def apply_material_effect(self, base_color, glossiness, metalness, transparency):
        """应用材质效果到颜色"""
        import numpy as np

        color = np.array(base_color[:3])  # RGB部分
        alpha = base_color[3] * (1.0 - transparency)  # 应用透明度

        if self.current_material_type == "Metallic":
            # 金属效果：增加反射，降低饱和度
            color = color * (1 - metalness * 0.3) + np.array([0.9, 0.9, 0.9]) * metalness * 0.3
            color = color * (1 - glossiness * 0.2) + np.array([1.0, 1.0, 1.0]) * glossiness * 0.2

        elif self.current_material_type == "Plastic":
            # 塑料效果：增加饱和度和光泽
            color = np.clip(color * (1 + glossiness * 0.3), 0, 1)

        elif self.current_material_type == "Glass":
            # 玻璃效果：增加透明度和反射
            color = color * 0.8 + np.array([0.9, 0.9, 1.0]) * 0.2
            alpha = min(alpha, 0.7)

        elif self.current_material_type == "Matte":
            # 哑光效果：降低光泽，增加漫反射
            color = color * 0.9

        elif self.current_material_type == "Glossy":
            # 光滑效果：增加反射
            color = color * (1 - glossiness * 0.1) + np.array([1.0, 1.0, 1.0]) * glossiness * 0.1

        elif self.current_material_type == "Skin":
            # 皮肤效果：温暖的色调
            color = color * 0.8 + np.array([1.0, 0.8, 0.7]) * 0.2

        elif self.current_material_type == "Robot":
            # 机器人效果：金属灰色调
            color = color * 0.7 + np.array([0.7, 0.8, 0.9]) * 0.3

        return tuple(np.clip(color, 0, 1).tolist() + [alpha])

    def apply_material_preset(self, preset_name):
        """应用材质预设"""
        presets = {
            "chrome": {
                "material_type": "Metallic",
                "glossiness": 90,
                "metalness": 80,
                "transparency": 0
            },
            "gold": {
                "material_type": "Metallic",
                "glossiness": 85,
                "metalness": 90,
                "transparency": 0
            },
            "plastic": {
                "material_type": "Plastic",
                "glossiness": 60,
                "metalness": 0,
                "transparency": 0
            }
        }

        if preset_name in presets:
            preset = presets[preset_name]

            # 设置材质类型
            self.material_type_combo.setCurrentText(preset["material_type"])

            # 设置滑块值
            self.glossiness_slider.setValue(preset["glossiness"])
            self.metalness_slider.setValue(preset["metalness"])
            self.transparency_slider.setValue(preset["transparency"])

            # 更新材质
            self.update_material_properties()

    def toggle_edges(self):
        """切换边框显示"""
        show_edges = self.show_edges_checkbox.isChecked()
        self.recreate_meshes_with_edges(show_edges)

    def toggle_wireframe(self):
        """切换线框模式"""
        wireframe = self.wireframe_checkbox.isChecked()
        if wireframe:
            self.recreate_meshes_wireframe()
        else:
            self.recreate_meshes_with_edges(self.show_edges_checkbox.isChecked())

    def recreate_meshes_with_edges(self, show_edges):
        """重新创建网格项以显示/隐藏边框"""
        if not self.mesh_items:
            return

        # 保存当前相机状态
        current_camera = self.view_widget.cameraParams()
        self.view_update_blocked = True

        # 保存当前网格数据和变换
        mesh_data_backup = {}
        for item in self.mesh_items:
            if hasattr(item, 'link_name'):
                link_name = item.link_name
                if hasattr(item, '_vertexes') and hasattr(item, '_faces'):
                    mesh_data_backup[link_name] = {
                        'vertices': item._vertexes,
                        'faces': item._faces
                    }

        # 移除旧的网格项
        for item in self.mesh_items:
            self.view_widget.removeItem(item)
        self.mesh_items.clear()

        # 重新创建网格项
        for link_name, data in mesh_data_backup.items():
            mesh_item = gl.GLMeshItem(
                vertexes=data['vertices'],
                faces=data['faces'],
                smooth=True,
                drawEdges=show_edges,
                edgeColor=(0.2, 0.2, 0.2, 0.8) if show_edges else (0, 0, 0, 0),
                shader='shaded',
                glOptions='opaque'
            )
            mesh_item.link_name = link_name
            self.apply_link_material(mesh_item, link_name)
            self.view_widget.addItem(mesh_item)
            self.mesh_items.append(mesh_item)

        # 恢复相机状态
        if current_camera:
            self.view_widget.setCameraParams(**current_camera)

        self.view_update_blocked = False
        self.view_widget.update()

    def recreate_meshes_wireframe(self):
        """重新创建网格项为线框模式"""
        if not self.mesh_items:
            return

        # 保存当前相机状态
        current_camera = self.view_widget.cameraParams()
        self.view_update_blocked = True

        # 保存当前网格数据
        mesh_data_backup = {}
        for item in self.mesh_items:
            if hasattr(item, 'link_name'):
                link_name = item.link_name
                if hasattr(item, '_vertexes') and hasattr(item, '_faces'):
                    mesh_data_backup[link_name] = {
                        'vertices': item._vertexes,
                        'faces': item._faces
                    }

        # 移除旧的网格项
        for item in self.mesh_items:
            self.view_widget.removeItem(item)
        self.mesh_items.clear()

        # 重新创建为线框模式
        for link_name, data in mesh_data_backup.items():
            mesh_item = gl.GLMeshItem(
                vertexes=data['vertices'],
                faces=data['faces'],
                smooth=False,
                drawFaces=False,
                drawEdges=True,
                edgeColor=(0.8, 0.8, 0.8, 1.0),
                shader='shaded',
                glOptions='opaque'
            )
            mesh_item.link_name = link_name
            self.view_widget.addItem(mesh_item)
            self.mesh_items.append(mesh_item)

        # 恢复相机状态
        if current_camera:
            self.view_widget.setCameraParams(**current_camera)

        self.view_update_blocked = False
        self.view_widget.update()

    def choose_link_color(self):
        """选择链接颜色"""
        color = QColorDialog.getColor(self.link_color, self, "选择链接颜色")
        if color.isValid():
            self.link_color = color
            # 更新所有链接的基础颜色
            for link_name in self.link_materials:
                self.link_materials[link_name]['color'] = [
                    color.red()/255.0, color.green()/255.0,
                    color.blue()/255.0, color.alpha()/255.0
                ]
            self.update_material_properties()

    def choose_joint_color(self):
        """选择关节颜色"""
        color = QColorDialog.getColor(self.joint_color, self, "选择关节颜色")
        if color.isValid():
            self.joint_color = color
            # 这里可以添加更新关节颜色的逻辑

    def toggle_grid(self):
        """切换网格显示"""
        if self.view_update_blocked:
            return

        show_grid = self.show_grid_checkbox.isChecked()

        if show_grid:
            if self.grid_item is None:
                # 创建网格
                self.grid_item = gl.GLGridItem()
                self.grid_item.scale(0.1, 0.1, 0.1)  # 缩小网格
                self.grid_item.setColor((0.3, 0.3, 0.3, 0.5))  # 半透明灰色网格
            self.view_widget.addItem(self.grid_item)
        else:
            if self.grid_item is not None:
                self.view_widget.removeItem(self.grid_item)

        # 使用定时器延迟更新
        if not self.pending_update:
            self.pending_update = True
            self.update_timer.start()

    def toggle_axis(self):
        """切换坐标轴显示"""
        if self.view_update_blocked:
            return

        show_axis = self.show_axis_checkbox.isChecked()

        if show_axis:
            if self.axis_item is None:
                # 创建坐标轴
                self.axis_item = gl.GLAxisItem()
                self.axis_item.setSize(0.05, 0.05, 0.05)  # 缩小坐标轴
            self.view_widget.addItem(self.axis_item)
        else:
            if self.axis_item is not None:
                self.view_widget.removeItem(self.axis_item)

        # 使用定时器延迟更新
        if not self.pending_update:
            self.pending_update = True
            self.update_timer.start()

    def save_urdf_image(self):
        """
        保存URDF模型的当前视图为图片

        抓取3D视图的当前画面并保存为PNG格式的图片文件。
        用户可以选择保存位置和文件名。
        """
        if not self.robot:
            self.status_label.setText("错误: 请先加载URDF文件")
            return

        try:
            # 获取当前时间作为默认文件名
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"urdf_model_{timestamp}.png"

            # 打开文件保存对话框
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "保存URDF图片",
                default_filename,
                "PNG Files (*.png);;JPEG Files (*.jpg);;All Files (*)"
            )

            if file_path:
                # 抓取3D视图的画面
                # 使用QWidget的grab方法，这是最可靠的方法
                try:
                    # 方法1: 尝试使用PyQtGraph的内置方法
                    success = False

                    # 确保视图已经完全渲染
                    self.view_widget.update()
                    QApplication.processEvents()

                    # 尝试使用PyQtGraph的readQImage方法（如果存在）
                    if hasattr(self.view_widget, 'readQImage'):
                        try:
                            image = self.view_widget.readQImage()
                            if image and not image.isNull():
                                if image.save(file_path):
                                    success = True
                        except:
                            pass

                    # 方法2: 如果方法1失败，使用grab方法
                    if not success:
                        pixmap = self.view_widget.grab()
                        if pixmap and not pixmap.isNull():
                            # 创建一个带有正确背景色的新图像
                            image = self.create_image_with_background(pixmap)
                            if image.save(file_path):
                                success = True

                    if success:
                        self.status_label.setText(f"图片已保存: {os.path.basename(file_path)}")
                        print(f"URDF图片已保存到: {file_path}")
                    else:
                        self.status_label.setText("错误: 图片保存失败")
                        print("图片保存失败")
                    # else:
                    #     # 如果grab失败，使用屏幕截图作为备用方案
                    #     print("Widget grab失败，尝试屏幕截图方案")
                    #
                    #     # 获取视图widget的全局位置和大小
                    #     global_pos = self.view_widget.mapToGlobal(self.view_widget.rect().topLeft())
                    #     size = self.view_widget.size()
                    #
                    #     # 截取屏幕上对应区域
                    #     screen = QApplication.primaryScreen()
                    #     screenshot = screen.grabWindow(0, global_pos.x(), global_pos.y(),
                    #                                  size.width(), size.height())
                    #
                    #     if screenshot and not screenshot.isNull():
                    #         if screenshot.save(file_path):
                    #             self.status_label.setText(f"图片已保存: {os.path.basename(file_path)}")
                    #             print(f"URDF图片已保存到: {file_path}")
                    #         else:
                    #             self.status_label.setText("错误: 图片保存失败")
                    #             print("图片保存失败")
                    #     else:
                    #         self.status_label.setText("错误: 无法抓取图像")
                    #         print("无法抓取图像")

                except Exception as grab_error:
                    print(f"抓取图像时出错: {grab_error}")
                    self.status_label.setText(f"抓取图像失败: {str(grab_error)}")
                    import traceback
                    print(traceback.format_exc())
            else:
                self.status_label.setText("图片保存已取消")

        except Exception as e:
            error_msg = f"保存图片时出错: {str(e)}"
            self.status_label.setText(error_msg)
            print(error_msg)
            import traceback
            print("错误追踪:")
            print(traceback.format_exc())

    def create_image_with_background(self, pixmap):
        """
        创建带有正确背景颜色的图像

        Args:
            pixmap: QPixmap对象

        Returns:
            QImage: 带有正确背景的图像
        """
        from PySide6.QtGui import QPainter, QBrush

        # 转换为图像
        image = pixmap.toImage()

        # 创建一个新的图像，先填充背景色
        width = image.width()
        height = image.height()

        # 设置背景颜色 (0.15, 0.15, 0.2, 1.0) -> RGB(38, 38, 51)
        background_color = QColor(int(0.15 * 255), int(0.15 * 255), int(0.2 * 255))

        # 创建新图像并填充背景
        result_image = image.copy()
        painter = QPainter(result_image)

        # 设置合成模式，只在透明或白色区域绘制背景
        painter.setCompositionMode(QPainter.CompositionMode_DestinationOver)
        painter.fillRect(result_image.rect(), background_color)

        painter.end()
        return result_image

    def closeEvent(self, event):
        """关闭事件"""
        # 清理工作线程
        if self.load_worker and self.load_worker.isRunning():
            print("正在等待加载线程结束...")
            self.load_worker.quit()
            self.load_worker.wait(3000)  # 等待最多3秒
            if self.load_worker.isRunning():
                print("强制终止加载线程")
                self.load_worker.terminate()
                self.load_worker.wait(1000)

        # 清理其他资源
        if self.transform_calculator:
            self.transform_calculator = None

        super().closeEvent(event)


def main():
    """
    应用程序主函数

    初始化Qt应用程序，创建URDF查看器实例并启动事件循环。
    这是程序的入口点，负责整个应用程序的生命周期管理。
    """
    # 创建Qt应用程序实例
    app = QApplication(sys.argv)

    # 创建并显示URDF整合查看器主窗口
    viewer = URDFIntegratedViewer()
    viewer.show()

    # 启动Qt事件循环，直到用户关闭应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    """
    程序入口点

    当脚本直接运行时（而不是被导入时），执行主函数。
    这是Python模块的标准入口点模式。
    """
    main()