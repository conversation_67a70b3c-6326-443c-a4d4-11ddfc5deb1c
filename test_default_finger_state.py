#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试默认手指状态设置

验证URDF加载时手指的默认状态是否正确：
- 四指（食指、中指、无名指、小指）默认伸直状态
- 拇指转动默认外转
- 拇指弯曲默认伸直
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.urdf_parser import URDFJointAnalyzer, FingerGroupAnalyzer
import urdf_parser_py.urdf as urdf


def test_default_finger_state():
    """测试默认手指状态设置"""
    
    # 测试用的URDF文件路径
    test_urdf_files = [
        "brainco-righthand-URDF-V2/urdf/brainco-righthand-URDF-V2.urdf",
        "brainco-lefthand-URDF-V2/urdf/brainco-lefthand-URDF-V2.urdf"
    ]
    
    for urdf_file in test_urdf_files:
        if not os.path.exists(urdf_file):
            print(f"跳过不存在的文件: {urdf_file}")
            continue
            
        print(f"\n=== 测试文件: {urdf_file} ===")
        
        try:
            # 解析URDF文件
            robot = urdf.URDF.from_xml_file(urdf_file)
            
            # 创建关节分析器
            joint_analyzer = URDFJointAnalyzer(urdf_file)
            controllable_joints = joint_analyzer.get_controllable_joints()
            
            print(f"发现 {len(controllable_joints)} 个可控关节")
            
            # 模拟初始化关节值的逻辑
            joint_values = {}
            
            # 定义手指关节模式
            finger_joint_patterns = {
                'four_fingers': ['index_', 'middle_', 'ring_', 'pinky_'],
                'thumb_rotation': ['thumb_metacarpal_joint'],
                'thumb_bend': ['thumb_proximal_joint', 'thumb_distal_joint', 'thumb_tip_joint']
            }
            
            print("\n关节初始化结果:")
            print("-" * 50)
            
            for joint_name, joint_info in controllable_joints.items():
                lower = joint_info['lower']
                upper = joint_info['upper']
                
                # 检查是否为手指关节
                is_finger_joint = False
                joint_type = "其他关节"
                
                # 检查四指关节
                for finger_prefix in finger_joint_patterns['four_fingers']:
                    if finger_prefix in joint_name:
                        joint_values[joint_name] = lower
                        is_finger_joint = True
                        joint_type = "四指关节（伸直）"
                        break
                
                # 检查拇指转动关节
                if not is_finger_joint:
                    for thumb_rotation_pattern in finger_joint_patterns['thumb_rotation']:
                        if thumb_rotation_pattern in joint_name:
                            joint_values[joint_name] = lower
                            is_finger_joint = True
                            joint_type = "拇指转动（外转）"
                            break
                
                # 检查拇指弯曲关节
                if not is_finger_joint:
                    for thumb_bend_pattern in finger_joint_patterns['thumb_bend']:
                        if thumb_bend_pattern in joint_name:
                            joint_values[joint_name] = lower
                            is_finger_joint = True
                            joint_type = "拇指弯曲（伸直）"
                            break
                
                # 非手指关节使用中点
                if not is_finger_joint:
                    if upper > lower:
                        joint_values[joint_name] = (lower + upper) / 2
                        joint_type = "其他关节（中点）"
                    else:
                        joint_values[joint_name] = lower
                        joint_type = "其他关节（下限）"
                
                # 输出结果
                value = joint_values[joint_name]
                print(f"{joint_name:30} | {joint_type:15} | 值: {value:8.3f} | 范围: [{lower:6.3f}, {upper:6.3f}]")
            
            # 统计各类关节数量
            four_finger_count = sum(1 for name in joint_values.keys() 
                                  if any(prefix in name for prefix in finger_joint_patterns['four_fingers']))
            thumb_rotation_count = sum(1 for name in joint_values.keys() 
                                     if any(pattern in name for pattern in finger_joint_patterns['thumb_rotation']))
            thumb_bend_count = sum(1 for name in joint_values.keys() 
                                 if any(pattern in name for pattern in finger_joint_patterns['thumb_bend']))
            
            print(f"\n统计结果:")
            print(f"四指关节数量: {four_finger_count}")
            print(f"拇指转动关节数量: {thumb_rotation_count}")
            print(f"拇指弯曲关节数量: {thumb_bend_count}")
            print(f"总关节数量: {len(joint_values)}")
            
        except Exception as e:
            print(f"测试文件 {urdf_file} 时出错: {e}")


if __name__ == "__main__":
    test_default_finger_state()
